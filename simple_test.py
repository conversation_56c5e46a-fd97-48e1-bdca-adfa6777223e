#!/usr/bin/env python3
"""简单测试脚本"""

import sys
import traceback

def test_imports():
    """测试导入"""
    print("测试基础导入...")
    
    try:
        import numpy as np
        print("✅ numpy")
    except Exception as e:
        print(f"❌ numpy: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ pandas")
    except Exception as e:
        print(f"❌ pandas: {e}")
        return False
    
    try:
        print("尝试导入Strategy3...")
        import Strategy3
        print("✅ Strategy3 导入成功")
        
        print("尝试创建Strategy3实例...")
        strategy = Strategy3.Strategy3()
        print("✅ Strategy3 实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy3: {e}")
        traceback.print_exc()
        return False

def test_basic_decision():
    """测试基础决策"""
    try:
        import Strategy3
        strategy = Strategy3.Strategy3()
        
        # 简单测试数据
        test_data = {
            'price': 100.0,
            'volume': 1000,
            'timestamp': 1234567890,
            'volatility': 0.02,
            'stability': 0.7,
            'profit': 0.01
        }
        
        print("执行决策测试...")
        decision = strategy.make_trading_decision(test_data)
        print(f"✅ 决策成功: {decision}")
        
        return True
        
    except Exception as e:
        print(f"❌ 决策测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 简单测试开始")
    print("=" * 40)
    
    if test_imports():
        print("\n📊 执行决策测试...")
        test_basic_decision()
    
    print("=" * 40)
    print("测试完成")

# ===== 核心导入模块 =====
from typing import Literal, Callable, List, Tuple, Dict, Any, Optional, Union
import numpy as np
from datetime import datetime, timedelta
import math
import time
import threading
import asyncio
import json
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from collections import defaultdict, deque
from queue import Queue, Empty
import gc

# ===== 科学计算库 =====
from scipy.linalg import solve
from scipy.optimize import minimize
from scipy.stats import norm

# ===== 平台特定导入 =====
try:
    from pythongo.base import BaseParams, BaseState, Field
    from pythongo.classdef import KLineData, OrderData, TickData, TradeData
    from pythongo.ui import BaseStrategy
    from pythongo.utils import KLineGenerator
    PYTHONGO_AVAILABLE = True
except ImportError:
    # 创建模拟类以支持独立运行
    class BaseParams:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class BaseState:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class Field:
        def __init__(self, default=None, **kwargs):
            self.default = default

    class BaseStrategy:
        def __init__(self):
            pass
        def output(self, msg):
            print(f"[Strategy] {msg}")

    # 模拟数据类
    class KLineData:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class OrderData:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class TickData:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class TradeData:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class KLineGenerator:
        @staticmethod
        def generate(**kwargs):
            return []

    PYTHONGO_AVAILABLE = False
    print("⚠️ pythongo 模块不可用，使用模拟实现")

# ===== 配置和系统管理 =====
import yaml
import os
from pathlib import Path
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    # 创建模拟psutil功能
    class MockPsutil:
        @staticmethod
        def cpu_percent():
            return 50.0
        @staticmethod
        def virtual_memory():
            class MockMemory:
                percent = 60.0
            return MockMemory()
    psutil = MockPsutil()

# ===== 可选依赖库导入 =====
# 智能优化库
try:
    from skopt import gp_minimize, Optimizer
    from skopt.space import Real, Integer
    from skopt.utils import use_named_args
    BAYESIAN_AVAILABLE = True
except ImportError:
    BAYESIAN_AVAILABLE = False
    print("警告: scikit-optimize未安装，贝叶斯优化功能将不可用")

# 遗传算法库
try:
    from deap import base, creator, tools, algorithms
    import random
    GENETIC_AVAILABLE = True
except ImportError:
    GENETIC_AVAILABLE = False
    print("警告: DEAP未安装，遗传算法功能将不可用")

# ===== 核心架构定义 =====

@dataclass
class SystemConfig:
    """系统配置数据类"""
    # 模糊系统配置
    fuzzy_sets: Dict[str, Dict[str, Dict[str, float]]] = field(default_factory=dict)
    learning_parameters: Dict[str, Union[float, bool, int]] = field(default_factory=dict)
    fusion_weights: Dict[str, float] = field(default_factory=dict)
    async_processing: Dict[str, Union[int, float]] = field(default_factory=dict)

    # 交易配置
    trading_parameters: Dict[str, Union[float, int, bool]] = field(default_factory=dict)
    risk_management: Dict[str, Union[float, int]] = field(default_factory=dict)

    # 优化配置
    optimization: Dict[str, Union[bool, int, float]] = field(default_factory=dict)

class IConfigurable(ABC):
    """可配置接口"""
    @abstractmethod
    def update_config(self, config: SystemConfig) -> None:
        """更新配置"""
        pass

    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        pass

class IProcessor(ABC):
    """处理器接口"""
    @abstractmethod
    def process(self, data: Any) -> Any:
        """处理数据"""
        pass

    @abstractmethod
    def validate_input(self, data: Any) -> bool:
        """验证输入数据"""
        pass

class IPredictor(ABC):
    """预测器接口"""
    @abstractmethod
    def predict(self, features: np.ndarray) -> Tuple[float, float]:
        """预测：返回(预测值, 置信度)"""
        pass

    @abstractmethod
    def update(self, features: np.ndarray, target: float) -> None:
        """更新模型"""
        pass

# ===== 配置管理系统 =====

class ConfigManager:
    """统一配置管理器"""

    def __init__(self, config_file: str = "fuzzy_config.yaml"):
        self.config_file = config_file
        self.config = SystemConfig()
        self.watchers: List[IConfigurable] = []
        self.file_watcher_active = False
        self.logger = logging.getLogger(__name__)
        self._load_default_config()

    def _load_default_config(self) -> None:
        """加载默认配置"""

        """加载默认配置"""
        # 模糊集合配置
        self.config.fuzzy_sets = {
            'stability': {
                'Low': {'a': 0.0, 'b': 0.1, 'c': 0.3, 'd': 0.4},
                'Medium': {'a': 0.3, 'b': 0.4, 'c': 0.6, 'd': 0.7},
                'High': {'a': 0.6, 'b': 0.7, 'c': 0.9, 'd': 1.0}
            },
            'volatility': {
                'Low': {'a': 0.0, 'b': 0.01, 'c': 0.03, 'd': 0.05},
                'Medium': {'a': 0.03, 'b': 0.05, 'c': 0.07, 'd': 0.09},
                'High': {'a': 0.07, 'b': 0.09, 'c': 0.12, 'd': 0.15}
            },
            'profit': {
                'Negative': {'a': -0.1, 'b': -0.08, 'c': -0.03, 'd': 0.0},
                'Low': {'a': -0.02, 'b': 0.0, 'c': 0.02, 'd': 0.04},
                'Medium': {'a': 0.02, 'b': 0.04, 'c': 0.06, 'd': 0.08},
                'High': {'a': 0.06, 'b': 0.08, 'c': 0.12, 'd': 0.15}
            }
        }

        # 学习参数配置
        self.config.learning_parameters = {
            'learning_rate': 0.01,
            'adaptation_enabled': True,
            'max_rules': 30,
            'rule_pruning_threshold': 0.01
        }

        # 信息融合权重配置
        self.config.fusion_weights = {
            'technical': 0.4,
            'sentiment': 0.3,
            'fundamental': 0.2,
            'news': 0.1
        }

        # 异步处理配置
        self.config.async_processing = {
            'max_workers': 4,
            'batch_size': 10,
            'queue_timeout': 1.0
        }

        # 交易参数配置
        self.config.trading_parameters = {
            'min_request_interval': 0.001,  # 1ms
            'max_requests_per_second': 1000,
            'complex_calc_interval': 0.1,   # 100ms
            'decision_update_interval': 60   # 60 ticks
        }

        # 风险管理配置
        self.config.risk_management = {
            'max_position_size': 1.0,
            'var_threshold': 0.05,
            'es_threshold': 0.08,
            'max_drawdown': 0.15
        }

        # 优化配置
        self.config.optimization = {
            'bayesian_enabled': BAYESIAN_AVAILABLE,
            'genetic_enabled': GENETIC_AVAILABLE,
            'automl_enabled': False,
            'optimization_interval': 1000  # ticks
        }

    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            keys = key_path.split('.')
            if len(keys) == 1:
                # 顶级配置
                return getattr(self.config, keys[0], default)
            elif len(keys) == 2:
                # 二级配置
                section = getattr(self.config, keys[0], {})
                if isinstance(section, dict):
                    return section.get(keys[1], default)
            return default
        except (KeyError, TypeError, AttributeError):
            return default

    def set(self, key_path: str, value: Any) -> None:
        """设置配置值"""
        try:
            keys = key_path.split('.')
            if len(keys) == 1:
                # 顶级配置
                if hasattr(self.config, keys[0]):
                    setattr(self.config, keys[0], value)
            elif len(keys) == 2:
                # 二级配置
                section = getattr(self.config, keys[0], {})
                if isinstance(section, dict):
                    section[keys[1]] = value
                    setattr(self.config, keys[0], section)
            self._notify_watchers(key_path, value)
        except Exception as e:
            self.logger.error(f"设置配置失败: {e}")

    def update(self, updates: Dict[str, Any]) -> None:
        """批量更新配置"""
        for key_path, value in updates.items():
            self.set(key_path, value)

    def register_watcher(self, watcher: IConfigurable) -> None:
        """注册配置监听器"""
        self.watchers.append(watcher)

    def _notify_watchers(self, key_path: str, value: Any) -> None:
        """通知监听器"""
        for watcher in self.watchers:
            try:
                watcher.update_config(self.config)
            except Exception as e:
                self.logger.error(f"配置监听器错误: {e}")

    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'learning_rate': self.get('learning_parameters.learning_rate'),
            'max_rules': self.get('learning_parameters.max_rules'),
            'async_workers': self.get('async_processing.max_workers'),
            'bayesian_enabled': self.get('optimization.bayesian_enabled'),
            'genetic_enabled': self.get('optimization.genetic_enabled')
        }

# 全局配置管理器实例
config_manager = ConfigManager()

# ===== 可视化模块 =====
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.animation import FuncAnimation
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

class VisualizationEngine:
    """统一可视化引擎"""

    def __init__(self):
        self.figure_cache: Dict[str, Any] = {}
        self.color_palette = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD"]
        self.matplotlib_available = MATPLOTLIB_AVAILABLE
        self.plotly_available = PLOTLY_AVAILABLE

    def plot_membership_functions(self, fuzzy_sets, title="隶属函数"):
        """绘制隶属函数"""
        if not MATPLOTLIB_AVAILABLE:
            return None

        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            x = np.linspace(0, 1, 1000)

            for i, (name, fuzzy_set) in enumerate(fuzzy_sets.items()):
                if hasattr(fuzzy_set, 'membership'):
                    y = [fuzzy_set.membership(xi) for xi in x]
                    color = self.color_palette[i % len(self.color_palette)]
                    ax.plot(x, y, label=name, color=color, linewidth=2)

            ax.set_xlabel('输入值')
            ax.set_ylabel('隶属度')
            ax.set_title(title)
            ax.legend()
            ax.grid(True, alpha=0.3)

            return fig
        except Exception as e:
            print(f"隶属函数绘制失败: {e}")
            return None

    def plot_rule_performance(self, rule_data, title="规则性能"):
        """绘制规则性能"""
        if not MATPLOTLIB_AVAILABLE:
            return None

        try:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

            # 规则使用次数
            rule_ids = list(rule_data.keys())
            usage_counts = [data['usage_count'] for data in rule_data.values()]

            ax1.bar(rule_ids, usage_counts, color=self.color_palette[0])
            ax1.set_title('规则使用频率')
            ax1.set_xlabel('规则ID')
            ax1.set_ylabel('使用次数')

            # 规则性能历史
            for i, (rule_id, data) in enumerate(rule_data.items()):
                if data['performance_history']:
                    color = self.color_palette[i % len(self.color_palette)]
                    ax2.plot(data['performance_history'], label=f'规则{rule_id}', color=color)

            ax2.set_title('规则性能历史')
            ax2.set_xlabel('时间步')
            ax2.set_ylabel('性能得分')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()
            return fig
        except Exception as e:
            print(f"规则性能绘制失败: {e}")
            return None

    def plot_time_series_prediction(self, historical_data, predictions, title="时间序列预测"):
        """绘制时间序列预测"""
        if not MATPLOTLIB_AVAILABLE:
            return None

        try:
            fig, ax = plt.subplots(figsize=(12, 6))

            # 历史数据
            hist_x = range(len(historical_data))
            ax.plot(hist_x, historical_data, label='历史数据', color=self.color_palette[0], linewidth=2)

            # 预测数据
            pred_x = range(len(historical_data), len(historical_data) + len(predictions))
            ax.plot(pred_x, predictions, label='预测数据', color=self.color_palette[1],
                   linewidth=2, linestyle='--', marker='o')

            # 连接线
            ax.plot([len(historical_data)-1, len(historical_data)],
                   [historical_data[-1], predictions[0]],
                   color=self.color_palette[2], linewidth=1, alpha=0.7)

            ax.set_xlabel('时间步')
            ax.set_ylabel('价格')
            ax.set_title(title)
            ax.legend()
            ax.grid(True, alpha=0.3)

            return fig
        except Exception as e:
            print(f"时间序列预测绘制失败: {e}")
            return None

# 全局可视化实例
visualization_engine = VisualizationEngine()

# ===== 集成异步处理模块 =====
from concurrent.futures import ThreadPoolExecutor, as_completed

class AsyncFuzzyProcessor:
    """异步模糊决策处理器 - 集成版"""

    def __init__(self, max_workers=4, batch_size=10):
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # 任务队列和节流机制
        self.decision_queue = Queue(maxsize=100)  # 限制队列大小
        self.result_queue = Queue()
        self.batch_buffer = []
        self.batch_lock = threading.Lock()

        # 节流控制
        self.last_request_time = 0
        self.min_request_interval = 0.001  # 最小请求间隔(1ms)
        self.request_count = 0
        self.max_requests_per_second = 1000

        # 性能监控
        self.processing_times = []
        self.throughput_counter = 0
        self.last_throughput_time = time.time()

        # 状态管理
        self.is_running = False
        self.background_tasks = []
        self.logger = logging.getLogger(__name__)

        # 初始化状态检测
        self._initialization_complete = False
        self._validate_initialization()

    def _validate_initialization(self):
        """验证初始化状态"""
        try:
            # 检查必要组件
            if self.executor is None:
                raise RuntimeError("线程池执行器初始化失败")
            if self.decision_queue is None:
                raise RuntimeError("决策队列初始化失败")

            # 测试线程池
            future = self.executor.submit(lambda: True)
            if not future.result(timeout=1.0):
                raise RuntimeError("线程池测试失败")

            self._initialization_complete = True
            self.logger.info("异步处理器初始化完成")

        except Exception as e:
            self.logger.error(f"异步处理器初始化失败: {e}")
            self._initialization_complete = False

    def _check_throttle(self):
        """检查节流限制"""
        current_time = time.time()

        # 检查请求间隔
        if current_time - self.last_request_time < self.min_request_interval:
            return False

        # 检查每秒请求数
        if self.request_count >= self.max_requests_per_second:
            if current_time - self.last_throughput_time < 1.0:
                return False
            else:
                # 重置计数器
                self.request_count = 0
                self.last_throughput_time = current_time

        self.last_request_time = current_time
        self.request_count += 1
        return True

    async def submit_decision_request(self, fuzzy_system, input_data, priority=1):
        """提交模糊决策请求（带节流控制）"""
        if not self._initialization_complete:
            raise RuntimeError("异步处理器未正确初始化")

        # 节流检查
        if not self._check_throttle():
            await asyncio.sleep(self.min_request_interval)

        try:
            request = {
                'id': f"req_{datetime.now().timestamp()}",
                'fuzzy_system': fuzzy_system,
                'input_data': input_data,
                'priority': priority,
                'timestamp': datetime.now(),
                'future': asyncio.Future()
            }

            # 非阻塞放入队列
            if self.decision_queue.full():
                # 队列满时，移除最旧的请求
                try:
                    old_request = self.decision_queue.get_nowait()
                    if not old_request['future'].done():
                        old_request['future'].set_exception(
                            RuntimeError("请求被丢弃：队列已满")
                        )
                except Empty:
                    pass

            self.decision_queue.put_nowait(request)
            return await request['future']

        except Exception as e:
            self.logger.error(f"提交决策请求失败: {e}")
            raise

    def _execute_fuzzy_inference(self, fuzzy_system, input_data):
        """执行模糊推理（在线程池中运行）"""
        try:
            if hasattr(fuzzy_system, 'adaptive_inference'):
                return fuzzy_system.adaptive_inference(*input_data)
            elif hasattr(fuzzy_system, 'infer'):
                return fuzzy_system.infer(*input_data)
            else:
                raise ValueError("模糊系统不支持推理方法")
        except Exception as e:
            self.logger.error(f"模糊推理执行失败: {e}")
            raise

    def get_performance_stats(self):
        """获取性能统计"""
        return {
            'avg_processing_time': np.mean(self.processing_times) if self.processing_times else 0,
            'max_processing_time': np.max(self.processing_times) if self.processing_times else 0,
            'min_processing_time': np.min(self.processing_times) if self.processing_times else 0,
            'queue_size': self.decision_queue.qsize(),
            'batch_buffer_size': len(self.batch_buffer),
            'is_running': self.is_running,
            'initialization_complete': self._initialization_complete,
            'requests_per_second': self.request_count
        }

# 全局异步处理器实例
async_fuzzy_processor = AsyncFuzzyProcessor()

# 设置模块可用性标志
FUZZY_MODULES_AVAILABLE = True

# ===== 模块间接口规范 =====
class ModuleInterface:
    """模块间接口规范基类"""

    def __init__(self, module_name):
        self.module_name = module_name
        self.logger = logging.getLogger(f"{__name__}.{module_name}")
        self.status = "initialized"
        self.last_update = datetime.now()

    def validate_input(self, data):
        """验证输入数据"""
        raise NotImplementedError("子类必须实现validate_input方法")

    def process(self, data):
        """处理数据"""
        raise NotImplementedError("子类必须实现process方法")

    def get_status(self):
        """获取模块状态"""
        return {
            'module_name': self.module_name,
            'status': self.status,
            'last_update': self.last_update
        }

class RiskDecisionInterface(ModuleInterface):
    """风控模块与决策模块交互接口"""

    def __init__(self):
        super().__init__("RiskDecisionInterface")
        self.risk_signals = Queue(maxsize=100)
        self.decision_feedback = Queue(maxsize=100)

    def validate_input(self, data):
        """验证风险数据输入"""
        required_fields = ['position', 'price', 'volatility', 'var']
        if not isinstance(data, dict):
            raise ValueError("输入数据必须是字典类型")

        for field in required_fields:
            if field not in data:
                raise ValueError(f"缺少必需字段: {field}")

        # 数值验证
        if not isinstance(data['position'], (int, float)):
            raise ValueError("position必须是数值类型")
        if not isinstance(data['price'], (int, float)) or data['price'] <= 0:
            raise ValueError("price必须是正数")
        if not isinstance(data['volatility'], (int, float)) or data['volatility'] < 0:
            raise ValueError("volatility必须是非负数")
        if not isinstance(data['var'], (int, float)):
            raise ValueError("var必须是数值类型")

        return True

    def process(self, risk_data):
        """处理风险数据并生成决策建议"""
        try:
            self.validate_input(risk_data)

            # 计算风险等级
            risk_level = self._calculate_risk_level(risk_data)

            # 生成决策建议
            decision_advice = self._generate_decision_advice(risk_level, risk_data)

            # 发送风险信号
            risk_signal = {
                'timestamp': datetime.now(),
                'risk_level': risk_level,
                'advice': decision_advice,
                'data': risk_data
            }

            if not self.risk_signals.full():
                self.risk_signals.put_nowait(risk_signal)

            self.status = "active"
            self.last_update = datetime.now()

            return decision_advice

        except Exception as e:
            self.logger.error(f"风险决策处理失败: {e}")
            self.status = "error"
            raise

    def _calculate_risk_level(self, risk_data):
        """计算风险等级"""
        var_threshold = 0.05  # 5% VaR阈值
        volatility_threshold = 0.3  # 30% 波动率阈值

        if abs(risk_data['var']) > var_threshold or risk_data['volatility'] > volatility_threshold:
            return "high"
        elif abs(risk_data['var']) > var_threshold * 0.5 or risk_data['volatility'] > volatility_threshold * 0.5:
            return "medium"
        else:
            return "low"

    def _generate_decision_advice(self, risk_level, risk_data):
        """生成决策建议"""
        if risk_level == "high":
            return {
                'action': 'reduce_position',
                'urgency': 'high',
                'max_position_ratio': 0.3,
                'stop_loss_adjustment': 0.8
            }
        elif risk_level == "medium":
            return {
                'action': 'maintain_caution',
                'urgency': 'medium',
                'max_position_ratio': 0.6,
                'stop_loss_adjustment': 0.9
            }
        else:
            return {
                'action': 'normal_operation',
                'urgency': 'low',
                'max_position_ratio': 1.0,
                'stop_loss_adjustment': 1.0
            }

    def get_risk_signals(self):
        """获取风险信号"""
        signals = []
        while not self.risk_signals.empty():
            try:
                signals.append(self.risk_signals.get_nowait())
            except Empty:
                break
        return signals

    def send_decision_feedback(self, feedback):
        """发送决策反馈"""
        if not self.decision_feedback.full():
            self.decision_feedback.put_nowait({
                'timestamp': datetime.now(),
                'feedback': feedback
            })

class FuzzyMLInterface(ModuleInterface):
    """模糊决策与ML预测交互接口"""

    def __init__(self):
        super().__init__("FuzzyMLInterface")
        self.prediction_cache = {}
        self.confidence_threshold = 0.6

    def validate_input(self, data):
        """验证模糊ML数据输入"""
        if not isinstance(data, dict):
            raise ValueError("输入数据必须是字典类型")

        required_fields = ['fuzzy_output', 'ml_prediction', 'market_data']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"缺少必需字段: {field}")

        return True

    def process(self, data):
        """融合模糊决策和ML预测"""
        try:
            self.validate_input(data)

            fuzzy_output = data['fuzzy_output']
            ml_prediction = data['ml_prediction']
            market_data = data['market_data']

            # 计算融合权重
            fuzzy_confidence = fuzzy_output.get('confidence', 0.5)
            ml_confidence = ml_prediction.get('confidence', 0.5)

            # 动态权重分配
            total_confidence = fuzzy_confidence + ml_confidence
            if total_confidence > 0:
                fuzzy_weight = fuzzy_confidence / total_confidence
                ml_weight = ml_confidence / total_confidence
            else:
                fuzzy_weight = ml_weight = 0.5

            # 融合决策
            fused_decision = self._fuse_decisions(
                fuzzy_output, ml_prediction, fuzzy_weight, ml_weight
            )

            # 缓存预测结果
            cache_key = f"{datetime.now().strftime('%Y%m%d%H%M')}"
            self.prediction_cache[cache_key] = fused_decision

            # 清理旧缓存
            if len(self.prediction_cache) > 100:
                oldest_key = min(self.prediction_cache.keys())
                del self.prediction_cache[oldest_key]

            self.status = "active"
            self.last_update = datetime.now()

            return fused_decision

        except Exception as e:
            self.logger.error(f"模糊ML融合失败: {e}")
            self.status = "error"
            raise

    def _fuse_decisions(self, fuzzy_output, ml_prediction, fuzzy_weight, ml_weight):
        """融合决策"""
        # 简化的决策融合逻辑
        fuzzy_action = fuzzy_output.get('action', 'hold')
        ml_action = ml_prediction.get('action', 'hold')

        # 如果两个预测一致，增加置信度
        if fuzzy_action == ml_action:
            confidence = min(0.95, (fuzzy_output.get('confidence', 0.5) + ml_prediction.get('confidence', 0.5)) / 2 + 0.1)
            return {
                'action': fuzzy_action,
                'confidence': confidence,
                'source': 'fused_consensus'
            }

        # 如果预测不一致，选择置信度更高的
        if fuzzy_output.get('confidence', 0.5) > ml_prediction.get('confidence', 0.5):
            return {
                'action': fuzzy_action,
                'confidence': fuzzy_output.get('confidence', 0.5) * 0.8,  # 降低置信度
                'source': 'fuzzy_dominant'
            }
        else:
            return {
                'action': ml_action,
                'confidence': ml_prediction.get('confidence', 0.5) * 0.8,
                'source': 'ml_dominant'
            }

# 全局接口实例
risk_decision_interface = RiskDecisionInterface()
fuzzy_ml_interface = FuzzyMLInterface()

try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LinearRegression, Ridge
    from sklearn.svm import SVR
    from sklearn.model_selection import cross_val_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import mean_squared_error, r2_score
    AUTOML_AVAILABLE = True
except ImportError:
    AUTOML_AVAILABLE = False
    print("警告: scikit-learn未安装，AutoML功能将不可用")

try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    print("警告: Optuna未安装，高级优化功能将不可用")

# ===== 模糊逻辑核心模块 =====

@dataclass
class FuzzySet:
    """模糊集合数据类"""
    name: str
    a: float  # 梯形左下角
    b: float  # 梯形左上角
    c: float  # 梯形右上角
    d: float  # 梯形右下角

    def membership(self, x: float) -> float:
        """计算隶属度"""
        if x <= self.a or x >= self.d:
            return 0.0
        elif self.a < x <= self.b:
            return (x - self.a) / (self.b - self.a) if self.b != self.a else 1.0
        elif self.b < x <= self.c:
            return 1.0
        elif self.c < x < self.d:
            return (self.d - x) / (self.d - self.c) if self.d != self.c else 1.0
        return 0.0

class FuzzyVariable:
    """模糊变量"""

    def __init__(self, name: str, universe: Tuple[float, float]):
        self.name = name
        self.universe = universe  # (min, max)
        self.fuzzy_sets: Dict[str, FuzzySet] = {}

    def add_fuzzy_set(self, fuzzy_set: FuzzySet) -> None:
        """添加模糊集合"""
        self.fuzzy_sets[fuzzy_set.name] = fuzzy_set

    def fuzzify(self, value: float, temporal_spatial_membership=None, spatial_context=None) -> Dict[str, float]:
        """模糊化（支持时空动态隶属函数）"""
        memberships = {}
        for name, fuzzy_set in self.fuzzy_sets.items():
            if temporal_spatial_membership is not None and spatial_context is not None:
                # 使用时空动态隶属函数
                fuzzy_set_params = (fuzzy_set.a, fuzzy_set.b, fuzzy_set.c, fuzzy_set.d)
                membership_value = temporal_spatial_membership.compute_spatiotemporal_membership(
                    value, spatial_context, fuzzy_set_params
                )
            else:
                # 使用静态隶属函数
                membership_value = fuzzy_set.membership(value)
            memberships[name] = membership_value
        return memberships

class FuzzyRule:
    """模糊规则"""

    def __init__(self, rule_id: str, condition: Callable, action: Callable, weight: float = 1.0):
        self.rule_id = rule_id
        self.condition = condition
        self.action = action
        self.weight = weight
        self.activation_count = 0
        self.performance_score = 0.0
        self.last_activated = None

    def evaluate(self, inputs: Dict[str, Dict[str, float]]) -> Tuple[bool, float]:
        """评估规则条件"""
        try:
            activation_strength = self.condition(**inputs)
            if activation_strength > 0:
                self.activation_count += 1
                self.last_activated = datetime.now()
                return True, activation_strength * self.weight
            return False, 0.0
        except Exception as e:
            print(f"规则 {self.rule_id} 评估失败: {e}")
            return False, 0.0

    def execute(self) -> Tuple[str, str]:
        """执行规则动作"""
        try:
            return self.action()
        except Exception as e:
            print(f"规则 {self.rule_id} 执行失败: {e}")
            return ("RiskMedium", "Normal")

class FuzzyNeuralNetwork(IPredictor, IConfigurable):
    """模糊神经网络实现"""

    def __init__(self, config: SystemConfig):
        self.input_dim = 20
        self.hidden_dim = 50
        self.output_dim = 3
        self.learning_rate = config.learning_parameters.get('learning_rate', 0.001)

        # 初始化网络参数
        self._initialize_network()

        # 训练历史
        self.training_history: List[Dict[str, float]] = []
        self.loss_history: List[float] = []

    def _initialize_network(self) -> None:
        """初始化网络参数"""
        # 权重和偏置
        self.W1 = np.random.normal(0, 0.1, (self.input_dim, self.hidden_dim))
        self.b1 = np.zeros((1, self.hidden_dim))
        self.W2 = np.random.normal(0, 0.1, (self.hidden_dim, self.output_dim))
        self.b2 = np.zeros((1, self.output_dim))

        # 模糊隶属函数参数
        self.fuzzy_centers = np.random.uniform(-1, 1, (self.hidden_dim, self.input_dim))
        self.fuzzy_widths = np.ones((self.hidden_dim, self.input_dim)) * 0.5

    def predict(self, features: np.ndarray) -> Tuple[float, float]:
        """预测接口实现"""
        if features.ndim == 1:
            features = features.reshape(1, -1)

        output, _, _ = self.forward(features)
        prediction = np.argmax(output[0])
        confidence = np.max(output[0])

        return float(prediction), float(confidence)

    def update(self, features: np.ndarray, target: float) -> None:
        """更新模型接口实现"""
        if features.ndim == 1:
            features = features.reshape(1, -1)

        # 创建目标向量
        y = np.zeros((1, self.output_dim))
        y[0, int(target)] = 1.0

        # 前向传播
        output, hidden, fuzzy_out = self.forward(features)

        # 反向传播
        self.backward(features, y, output, hidden, fuzzy_out)

        # 记录损失
        loss = self._calculate_loss(output, y)
        self.loss_history.append(loss)

    def update_config(self, config: SystemConfig) -> None:
        """更新配置"""
        self.learning_rate = config.learning_parameters.get('learning_rate', self.learning_rate)

    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return {
            'input_dim': self.input_dim,
            'hidden_dim': self.hidden_dim,
            'output_dim': self.output_dim,
            'learning_rate': self.learning_rate
        }

    def fuzzy_membership(self, x: np.ndarray, centers: np.ndarray, widths: np.ndarray) -> np.ndarray:
        """计算模糊隶属度"""
        # 高斯隶属函数
        diff = x[:, np.newaxis, :] - centers[np.newaxis, :, :]
        membership = np.exp(-0.5 * np.sum((diff / widths[np.newaxis, :, :]) ** 2, axis=2))
        return membership

    def forward(self, x: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """前向传播"""
        # 模糊层
        fuzzy_output = self.fuzzy_membership(x, self.fuzzy_centers, self.fuzzy_widths)

        # 第一层
        z1 = np.dot(fuzzy_output, self.W1) + self.b1
        a1 = np.tanh(z1)  # 激活函数

        # 输出层
        z2 = np.dot(a1, self.W2) + self.b2
        a2 = self.softmax(z2)

        return a2, a1, fuzzy_output

    def softmax(self, x: np.ndarray) -> np.ndarray:
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)

    def backward(self, x: np.ndarray, y: np.ndarray, output: np.ndarray,
                 hidden: np.ndarray, fuzzy_out: np.ndarray) -> None:
        """反向传播"""
        if x is None or y is None or output is None or hidden is None or fuzzy_out is None:
            return

        m = x.shape[0]

        # 输出层梯度
        dz2 = output - y
        dW2 = np.dot(hidden.T, dz2) / m
        db2 = np.sum(dz2, axis=0, keepdims=True) / m

        # 隐藏层梯度
        da1 = np.dot(dz2, self.W2.T)
        if hidden is not None:
            dz1 = da1 * (1 - hidden ** 2)  # tanh导数
            if fuzzy_out is not None:
                dW1 = np.dot(fuzzy_out.T, dz1) / m
                db1 = np.sum(dz1, axis=0, keepdims=True) / m
            else:
                dW1 = np.zeros_like(self.W1)
                db1 = np.zeros_like(self.b1)
        else:
            dW1 = np.zeros_like(self.W1)
            db1 = np.zeros_like(self.b1)

        # 更新权重
        self.W2 -= self.learning_rate * dW2
        self.b2 -= self.learning_rate * db2
        self.W1 -= self.learning_rate * dW1
        self.b1 -= self.learning_rate * db1

        # 更新模糊参数
        self.update_fuzzy_parameters(x, dz1, fuzzy_out)

    def _calculate_loss(self, output: np.ndarray, target: np.ndarray) -> float:
        """计算损失"""
        return -np.mean(target * np.log(output + 1e-8))

    def update_fuzzy_parameters(self, x: np.ndarray, dz1: np.ndarray, fuzzy_out: np.ndarray) -> None:
        """更新模糊隶属函数参数"""
        if x is None or dz1 is None:
            return

        m = x.shape[0]

        for i in range(self.hidden_dim):
            # 计算梯度
            grad_centers = np.zeros_like(self.fuzzy_centers[i])
            grad_widths = np.zeros_like(self.fuzzy_widths[i])

            for j in range(m):
                diff = x[j] - self.fuzzy_centers[i]
                membership = fuzzy_out[j, i]

                # 中心点梯度
                grad_centers += dz1[j, i] * membership * diff / (self.fuzzy_widths[i] ** 2)

                # 宽度梯度
                grad_widths += dz1[j, i] * membership * (diff ** 2) / (self.fuzzy_widths[i] ** 3)

            # 更新参数
            self.fuzzy_centers[i] += self.learning_rate * grad_centers / m
            self.fuzzy_widths[i] += self.learning_rate * grad_widths / m

            # 限制宽度范围
            self.fuzzy_widths[i] = np.clip(self.fuzzy_widths[i], 0.1, 2.0)

class NeuralFuzzyHybridSystem(IProcessor, IConfigurable):
    """神经模糊混合架构系统"""

    def __init__(self, config: SystemConfig):
        self.config = config

        # 初始化组件
        self.fuzzy_neural_network = FuzzyNeuralNetwork(config)
        self.fuzzy_inference_engine = FuzzyInferenceEngine(config)
        self.temporal_spatial_membership = TemporalSpatialMembership()

        # 混合架构参数
        self.neural_weight = 0.6  # 神经网络权重
        self.fuzzy_weight = 0.4   # 模糊推理权重
        self.adaptation_rate = 0.01

        # 性能跟踪
        self.neural_performance_history = []
        self.fuzzy_performance_history = []
        self.hybrid_performance_history = []

        # 决策融合历史
        self.decision_fusion_history = []

        # 自适应权重调整
        self.weight_adaptation_enabled = True
        self.performance_window = 50

    def validate_input(self, data):
        """验证输入数据"""
        required_keys = ['stability', 'volatility', 'profit']
        return all(key in data for key in required_keys)

    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """混合系统处理"""
        if not self.validate_input(data):
            raise ValueError("输入数据格式错误")

        try:
            # 提取特征
            features = self._extract_features(data)

            # 神经网络预测
            neural_prediction, neural_confidence = self.fuzzy_neural_network.predict(features)

            # 模糊推理
            fuzzy_result = self.fuzzy_inference_engine.process(data)
            fuzzy_prediction = self._fuzzy_result_to_numeric(fuzzy_result)
            fuzzy_confidence = fuzzy_result.get('confidence', 0.5)

            # 决策融合
            hybrid_decision = self._fuse_decisions(
                neural_prediction, neural_confidence,
                fuzzy_prediction, fuzzy_confidence
            )

            # 自适应权重调整
            if self.weight_adaptation_enabled:
                self._adapt_weights(neural_confidence, fuzzy_confidence)

            # 构建结果
            result = {
                'hybrid_prediction': hybrid_decision['prediction'],
                'hybrid_confidence': hybrid_decision['confidence'],
                'neural_prediction': neural_prediction,
                'neural_confidence': neural_confidence,
                'fuzzy_result': fuzzy_result,
                'fuzzy_confidence': fuzzy_confidence,
                'neural_weight': self.neural_weight,
                'fuzzy_weight': self.fuzzy_weight,
                'decision_method': hybrid_decision['method']
            }

            # 记录决策融合历史
            self.decision_fusion_history.append({
                'timestamp': datetime.now(),
                'input': data,
                'result': result
            })

            # 保持历史记录大小
            if len(self.decision_fusion_history) > 100:
                self.decision_fusion_history.pop(0)

            return result

        except Exception as e:
            print(f"神经模糊混合系统处理失败: {e}")
            return {
                'hybrid_prediction': 0.5,
                'hybrid_confidence': 0.3,
                'error': str(e)
            }

    def _extract_features(self, data: Dict[str, Any]) -> np.ndarray:
        """提取神经网络特征"""
        # 基础特征
        basic_features = [
            data.get('stability', 0.5),
            data.get('volatility', 0.5),
            data.get('profit', 0.0)
        ]

        # 时空特征
        spatial_context = basic_features.copy()
        self.temporal_spatial_membership.update_temporal_data(basic_features)
        self.temporal_spatial_membership.update_spatial_data(spatial_context)

        # 获取时空统计特征
        adaptation_stats = self.temporal_spatial_membership.get_adaptation_statistics()
        temporal_features = [
            adaptation_stats.get('temporal_weight_avg', 0.5),
            adaptation_stats.get('spatial_weight_avg', 0.5),
            adaptation_stats.get('membership_variance', 0.1),
            adaptation_stats.get('adaptation_count', 0) / 100.0  # 归一化
        ]

        # 历史特征
        history_features = self._extract_history_features()

        # 组合所有特征
        all_features = basic_features + temporal_features + history_features

        # 确保特征维度为20
        while len(all_features) < 20:
            all_features.append(0.0)

        return np.array(all_features[:20])

    def _extract_history_features(self) -> List[float]:
        """提取历史特征"""
        if len(self.decision_fusion_history) < 2:
            return [0.0] * 13  # 13个历史特征

        recent_decisions = self.decision_fusion_history[-10:]

        # 计算历史统计特征
        neural_predictions = [d['result']['neural_prediction'] for d in recent_decisions]
        fuzzy_confidences = [d['result']['fuzzy_confidence'] for d in recent_decisions]
        hybrid_confidences = [d['result']['hybrid_confidence'] for d in recent_decisions]

        history_features = [
            np.mean(neural_predictions),
            np.std(neural_predictions),
            np.mean(fuzzy_confidences),
            np.std(fuzzy_confidences),
            np.mean(hybrid_confidences),
            np.std(hybrid_confidences),
            len(recent_decisions) / 10.0,  # 归一化的历史长度
            np.mean([self.neural_weight] * len(recent_decisions)),
            np.mean([self.fuzzy_weight] * len(recent_decisions)),
            self._calculate_decision_consistency(),
            self._calculate_prediction_trend(),
            self._calculate_confidence_trend(),
            self._calculate_weight_stability()
        ]

        return history_features

    def _fuzzy_result_to_numeric(self, fuzzy_result: Dict[str, Any]) -> float:
        """将模糊推理结果转换为数值"""
        risk_level = fuzzy_result.get('risk_level', 'RiskMedium')
        action_level = fuzzy_result.get('action_level', 'Normal')

        # 风险级别映射
        risk_map = {
            'RiskNone': 0.0,
            'RiskLow': 0.3,
            'RiskMedium': 0.6,
            'RiskHigh': 0.9
        }

        # 行动级别映射
        action_map = {
            'Stop': 0.0,
            'Conservative': 0.3,
            'Normal': 0.6,
            'Aggressive': 0.9
        }

        risk_value = risk_map.get(risk_level, 0.6)
        action_value = action_map.get(action_level, 0.6)

        # 组合风险和行动为单一数值
        return (risk_value + action_value) / 2.0

    def _fuse_decisions(self, neural_pred, neural_conf, fuzzy_pred, fuzzy_conf):
        """决策融合"""
        # 基于置信度的动态权重
        total_conf = neural_conf + fuzzy_conf
        if total_conf > 0:
            dynamic_neural_weight = neural_conf / total_conf
            dynamic_fuzzy_weight = fuzzy_conf / total_conf
        else:
            dynamic_neural_weight = 0.5
            dynamic_fuzzy_weight = 0.5

        # 结合静态权重和动态权重
        final_neural_weight = 0.7 * self.neural_weight + 0.3 * dynamic_neural_weight
        final_fuzzy_weight = 0.7 * self.fuzzy_weight + 0.3 * dynamic_fuzzy_weight

        # 归一化权重
        total_weight = final_neural_weight + final_fuzzy_weight
        if total_weight > 0:
            final_neural_weight /= total_weight
            final_fuzzy_weight /= total_weight

        # 融合预测
        fused_prediction = (final_neural_weight * neural_pred +
                           final_fuzzy_weight * fuzzy_pred)

        # 融合置信度
        fused_confidence = (final_neural_weight * neural_conf +
                           final_fuzzy_weight * fuzzy_conf)

        # 确定主导方法
        if abs(neural_conf - fuzzy_conf) > 0.3:
            method = 'neural_dominant' if neural_conf > fuzzy_conf else 'fuzzy_dominant'
        else:
            method = 'balanced_fusion'

        return {
            'prediction': fused_prediction,
            'confidence': fused_confidence,
            'method': method,
            'neural_weight_used': final_neural_weight,
            'fuzzy_weight_used': final_fuzzy_weight
        }

    def _adapt_weights(self, neural_conf, fuzzy_conf):
        """自适应权重调整"""
        if len(self.neural_performance_history) < 5:
            return

        # 计算最近性能
        recent_neural_perf = np.mean(self.neural_performance_history[-5:])
        recent_fuzzy_perf = np.mean(self.fuzzy_performance_history[-5:])

        # 基于性能调整权重
        if recent_neural_perf > recent_fuzzy_perf + 0.1:
            self.neural_weight = min(0.8, self.neural_weight + self.adaptation_rate)
            self.fuzzy_weight = 1.0 - self.neural_weight
        elif recent_fuzzy_perf > recent_neural_perf + 0.1:
            self.fuzzy_weight = min(0.8, self.fuzzy_weight + self.adaptation_rate)
            self.neural_weight = 1.0 - self.fuzzy_weight

        # 基于置信度微调
        conf_diff = neural_conf - fuzzy_conf
        if abs(conf_diff) > 0.2:
            adjustment = conf_diff * self.adaptation_rate * 0.5
            self.neural_weight = np.clip(self.neural_weight + adjustment, 0.2, 0.8)
            self.fuzzy_weight = 1.0 - self.neural_weight

    def _calculate_decision_consistency(self) -> float:
        """计算决策一致性"""
        if len(self.decision_fusion_history) < 3:
            return 0.5

        recent_methods = [d['result']['decision_method'] for d in self.decision_fusion_history[-10:]]

        # 计算方法一致性
        method_counts = {}
        for method in recent_methods:
            method_counts[method] = method_counts.get(method, 0) + 1

        max_count = max(method_counts.values()) if method_counts else 1
        consistency = max_count / len(recent_methods)

        return consistency

    def _calculate_prediction_trend(self) -> float:
        """计算预测趋势"""
        if len(self.decision_fusion_history) < 3:
            return 0.0

        recent_predictions = [d['result']['hybrid_prediction'] for d in self.decision_fusion_history[-5:]]

        if len(recent_predictions) < 2:
            return 0.0

        # 计算趋势斜率
        x = np.arange(len(recent_predictions))
        y = np.array(recent_predictions)

        if np.std(x) > 0:
            correlation = np.corrcoef(x, y)[0, 1]
            return correlation if not np.isnan(correlation) else 0.0

        return 0.0

    def _calculate_confidence_trend(self) -> float:
        """计算置信度趋势"""
        if len(self.decision_fusion_history) < 3:
            return 0.0

        recent_confidences = [d['result']['hybrid_confidence'] for d in self.decision_fusion_history[-5:]]

        if len(recent_confidences) < 2:
            return 0.0

        # 计算置信度变化趋势
        return (recent_confidences[-1] - recent_confidences[0]) / len(recent_confidences)

    def _calculate_weight_stability(self) -> float:
        """计算权重稳定性"""
        if len(self.decision_fusion_history) < 3:
            return 1.0

        recent_neural_weights = [d['result']['neural_weight'] for d in self.decision_fusion_history[-10:]]

        if len(recent_neural_weights) < 2:
            return 1.0

        # 权重变异系数（越小越稳定）
        weight_std = np.std(recent_neural_weights)
        weight_mean = np.mean(recent_neural_weights)

        if weight_mean > 0:
            cv = weight_std / weight_mean
            return max(0.0, 1.0 - cv)  # 转换为稳定性指标

        return 1.0

    def update_performance(self, neural_performance: float, fuzzy_performance: float, hybrid_performance: float):
        """更新性能指标"""
        self.neural_performance_history.append(neural_performance)
        self.fuzzy_performance_history.append(fuzzy_performance)
        self.hybrid_performance_history.append(hybrid_performance)

        # 保持性能历史在合理范围内
        max_history = self.performance_window
        if len(self.neural_performance_history) > max_history:
            self.neural_performance_history.pop(0)
        if len(self.fuzzy_performance_history) > max_history:
            self.fuzzy_performance_history.pop(0)
        if len(self.hybrid_performance_history) > max_history:
            self.hybrid_performance_history.pop(0)

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'neural_weight': self.neural_weight,
            'fuzzy_weight': self.fuzzy_weight,
            'adaptation_enabled': self.weight_adaptation_enabled,
            'decision_history_length': len(self.decision_fusion_history),
            'neural_performance_avg': np.mean(self.neural_performance_history) if self.neural_performance_history else 0.0,
            'fuzzy_performance_avg': np.mean(self.fuzzy_performance_history) if self.fuzzy_performance_history else 0.0,
            'hybrid_performance_avg': np.mean(self.hybrid_performance_history) if self.hybrid_performance_history else 0.0,
            'decision_consistency': self._calculate_decision_consistency(),
            'prediction_trend': self._calculate_prediction_trend(),
            'confidence_trend': self._calculate_confidence_trend(),
            'weight_stability': self._calculate_weight_stability()
        }

    def update_config(self, config: SystemConfig) -> None:
        """更新配置"""
        self.config = config
        self.fuzzy_neural_network.update_config(config)
        self.fuzzy_inference_engine.update_config(config)

        # 更新混合系统参数
        hybrid_params = config.learning_parameters
        self.adaptation_rate = hybrid_params.get('adaptation_rate', self.adaptation_rate)
        self.weight_adaptation_enabled = hybrid_params.get('weight_adaptation_enabled', True)
        self.performance_window = hybrid_params.get('performance_window', 50)

    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return {
            'neural_weight': self.neural_weight,
            'fuzzy_weight': self.fuzzy_weight,
            'adaptation_rate': self.adaptation_rate,
            'weight_adaptation_enabled': self.weight_adaptation_enabled,
            'performance_window': self.performance_window
        }

class TemporalSpatialMembership:
    """时空维度动态隶属函数"""

    def __init__(self, time_window=100, spatial_dimensions=3):
        self.time_window = time_window
        self.spatial_dimensions = spatial_dimensions

        # 时间序列数据存储
        self.temporal_data = []
        self.spatial_data = []
        self.timestamp_data = []

        # 动态参数
        self.temporal_weights = np.ones(time_window) / time_window
        self.spatial_weights = np.ones(spatial_dimensions) / spatial_dimensions

        # 自适应参数
        self.adaptation_rate = 0.01
        self.decay_factor = 0.95

        # 隶属函数参数历史
        self.membership_history = []

    def update_temporal_data(self, new_data, timestamp=None):
        """更新时间序列数据"""
        if timestamp is None:
            timestamp = datetime.now()

        self.temporal_data.append(new_data)
        self.timestamp_data.append(timestamp)

        # 保持时间窗口大小
        if len(self.temporal_data) > self.time_window:
            self.temporal_data.pop(0)
            self.timestamp_data.pop(0)

    def update_spatial_data(self, spatial_features):
        """更新空间特征数据"""
        self.spatial_data.append(spatial_features)

        # 保持空间数据窗口
        if len(self.spatial_data) > self.time_window:
            self.spatial_data.pop(0)

    def compute_temporal_membership(self, value, fuzzy_set_params):
        """计算时间维度动态隶属度"""
        if len(self.temporal_data) < 2:
            return self._static_membership(value, fuzzy_set_params)

        # 计算时间趋势
        recent_trend = self._calculate_temporal_trend()

        # 动态调整隶属函数参数
        adjusted_params = self._adjust_membership_params(fuzzy_set_params, recent_trend)

        # 计算隶属度
        membership = self._trapezoidal_membership(value, adjusted_params)

        return membership

    def compute_spatial_membership(self, value, spatial_context, fuzzy_set_params):
        """计算空间维度动态隶属度"""
        if len(self.spatial_data) < 2:
            return self._static_membership(value, fuzzy_set_params)

        # 计算空间相关性
        spatial_correlation = self._calculate_spatial_correlation(spatial_context)

        # 基于空间相关性调整参数
        adjusted_params = self._adjust_spatial_params(fuzzy_set_params, spatial_correlation)

        # 计算隶属度
        membership = self._trapezoidal_membership(value, adjusted_params)

        return membership

    def compute_spatiotemporal_membership(self, value, spatial_context, fuzzy_set_params):
        """计算时空联合动态隶属度"""
        # 时间维度隶属度
        temporal_membership = self.compute_temporal_membership(value, fuzzy_set_params)

        # 空间维度隶属度
        spatial_membership = self.compute_spatial_membership(value, spatial_context, fuzzy_set_params)

        # 时空融合权重
        temporal_weight = self._calculate_temporal_weight()
        spatial_weight = self._calculate_spatial_weight()

        # 加权融合
        combined_membership = (temporal_weight * temporal_membership +
                             spatial_weight * spatial_membership) / (temporal_weight + spatial_weight)

        # 记录隶属度历史
        self.membership_history.append({
            'timestamp': datetime.now(),
            'value': value,
            'temporal_membership': temporal_membership,
            'spatial_membership': spatial_membership,
            'combined_membership': combined_membership,
            'temporal_weight': temporal_weight,
            'spatial_weight': spatial_weight
        })

        # 保持历史记录大小
        if len(self.membership_history) > self.time_window:
            self.membership_history.pop(0)

        return combined_membership

    def _calculate_temporal_trend(self):
        """计算时间趋势"""
        if len(self.temporal_data) < 3:
            return 0.0

        # 使用线性回归计算趋势
        x = np.arange(len(self.temporal_data))
        y = np.array(self.temporal_data)

        # 简单线性回归
        n = len(x)
        sum_x = np.sum(x)
        sum_y = np.sum(y)
        sum_xy = np.sum(x * y)
        sum_x2 = np.sum(x * x)

        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)

        return slope

    def _calculate_spatial_correlation(self, spatial_context):
        """计算空间相关性"""
        if len(self.spatial_data) < 2:
            return 0.0

        # 计算当前空间特征与历史数据的相关性
        recent_spatial = np.array(self.spatial_data[-10:])  # 最近10个数据点

        if recent_spatial.shape[0] < 2:
            return 0.0

        # 计算平均相关性
        correlations = []
        for i in range(recent_spatial.shape[1]):
            if np.std(recent_spatial[:, i]) > 1e-6:  # 避免除零
                corr = np.corrcoef(recent_spatial[:, i], spatial_context[i:i+len(recent_spatial)])[0, 1]
                if not np.isnan(corr):
                    correlations.append(abs(corr))

        return np.mean(correlations) if correlations else 0.0

    def _adjust_membership_params(self, original_params, trend):
        """基于时间趋势调整隶属函数参数"""
        a, b, c, d = original_params

        # 根据趋势调整参数
        trend_factor = np.tanh(trend * 10)  # 限制调整幅度

        # 调整梯形的位置和宽度
        shift = trend_factor * 0.1 * (d - a)
        width_adjust = (1 + trend_factor * 0.2)

        new_a = a + shift
        new_b = b + shift
        new_c = c + shift
        new_d = d + shift

        # 调整宽度
        center = (new_b + new_c) / 2
        new_b = center - (center - new_b) * width_adjust
        new_c = center + (new_c - center) * width_adjust

        return (new_a, new_b, new_c, new_d)

    def _adjust_spatial_params(self, original_params, spatial_correlation):
        """基于空间相关性调整隶属函数参数"""
        a, b, c, d = original_params

        # 高相关性时增加确定性（缩小模糊区域）
        # 低相关性时增加不确定性（扩大模糊区域）
        correlation_factor = spatial_correlation

        center = (b + c) / 2
        half_width = (c - b) / 2

        # 调整模糊区域宽度
        new_half_width = half_width * (1 - correlation_factor * 0.3)

        new_b = center - new_half_width
        new_c = center + new_half_width

        # 调整支撑区域
        support_adjust = correlation_factor * 0.2
        new_a = a + (b - a) * support_adjust
        new_d = d - (d - c) * support_adjust

        return (new_a, new_b, new_c, new_d)

    def _calculate_temporal_weight(self):
        """计算时间维度权重"""
        if len(self.temporal_data) < 2:
            return 0.5

        # 基于时间序列的变异性计算权重
        variance = np.var(self.temporal_data[-10:])  # 最近10个点的方差

        # 高变异性时增加时间维度权重
        weight = 0.3 + 0.4 * np.tanh(variance * 10)

        return weight

    def _calculate_spatial_weight(self):
        """计算空间维度权重"""
        return 1.0 - self._calculate_temporal_weight()

    def _static_membership(self, value, params):
        """静态隶属函数（回退方案）"""
        return self._trapezoidal_membership(value, params)

    def _trapezoidal_membership(self, value, params):
        """梯形隶属函数"""
        a, b, c, d = params

        if value <= a or value >= d:
            return 0.0
        elif a < value <= b:
            return (value - a) / (b - a)
        elif b < value <= c:
            return 1.0
        elif c < value < d:
            return (d - value) / (d - c)
        else:
            return 0.0

    def get_adaptation_statistics(self):
        """获取自适应统计信息"""
        if not self.membership_history:
            return {}

        recent_history = self.membership_history[-20:]  # 最近20个记录

        return {
            'temporal_weight_avg': np.mean([h['temporal_weight'] for h in recent_history]),
            'spatial_weight_avg': np.mean([h['spatial_weight'] for h in recent_history]),
            'membership_variance': np.var([h['combined_membership'] for h in recent_history]),
            'adaptation_count': len(self.membership_history),
            'temporal_data_points': len(self.temporal_data),
            'spatial_data_points': len(self.spatial_data)
        }

class FuzzyInferenceEngine(IProcessor, IConfigurable):
    """统一模糊推理引擎"""

    def __init__(self, config: SystemConfig):
        self.config = config
        self.variables: Dict[str, FuzzyVariable] = {}
        self.rules: List[FuzzyRule] = []
        self.rule_performance: Dict[str, float] = {}

        # 初始化时空动态隶属函数
        self.temporal_spatial_membership = TemporalSpatialMembership()

        # 初始化模糊变量
        self._initialize_variables()
        self._initialize_rules()

        # 推理历史
        self.inference_history: List[Dict[str, Any]] = []

    def _initialize_variables(self) -> None:
        """初始化模糊变量"""
        # 稳定性变量
        stability = FuzzyVariable("stability", (0.0, 1.0))
        for name, params in self.config.fuzzy_sets.get('stability', {}).items():
            fuzzy_set = FuzzySet(name, params['a'], params['b'], params['c'], params['d'])
            stability.add_fuzzy_set(fuzzy_set)
        self.variables['stability'] = stability

        # 波动性变量
        volatility = FuzzyVariable("volatility", (0.0, 0.2))
        for name, params in self.config.fuzzy_sets.get('volatility', {}).items():
            fuzzy_set = FuzzySet(name, params['a'], params['b'], params['c'], params['d'])
            volatility.add_fuzzy_set(fuzzy_set)
        self.variables['volatility'] = volatility

        # 盈利变量
        profit = FuzzyVariable("profit", (-0.1, 0.15))
        for name, params in self.config.fuzzy_sets.get('profit', {}).items():
            fuzzy_set = FuzzySet(name, params['a'], params['b'], params['c'], params['d'])
            profit.add_fuzzy_set(fuzzy_set)
        self.variables['profit'] = profit

    def _initialize_rules(self) -> None:
        """初始化模糊规则"""
        # 基础规则集
        rules_config = [
            # 低稳定性规则
            {
                'id': 'low_stability_low_vol',
                'condition': lambda s, v, p: s.get("Low", 0) * v.get("Low", 0),
                'action': lambda: ("RiskLow", "Conservative")
            },
            {
                'id': 'low_stability_high_vol',
                'condition': lambda s, v, p: s.get("Low", 0) * v.get("High", 0),
                'action': lambda: ("RiskNone", "Stop")
            },
            # 中等稳定性规则
            {
                'id': 'medium_stability_low_vol_high_profit',
                'condition': lambda s, v, p: s.get("Medium", 0) * v.get("Low", 0) * p.get("High", 0),
                'action': lambda: ("RiskHigh", "Aggressive")
            },
            {
                'id': 'medium_stability_medium_vol',
                'condition': lambda s, v, p: s.get("Medium", 0) * v.get("Medium", 0),
                'action': lambda: ("RiskMedium", "Normal")
            },
            # 高稳定性规则
            {
                'id': 'high_stability_low_vol_high_profit',
                'condition': lambda s, v, p: s.get("High", 0) * v.get("Low", 0) * p.get("High", 0),
                'action': lambda: ("RiskHigh", "Aggressive")
            },
            {
                'id': 'high_stability_any_vol_negative_profit',
                'condition': lambda s, v, p: s.get("High", 0) * p.get("Negative", 0),
                'action': lambda: ("RiskNone", "Stop")
            }
        ]

        for rule_config in rules_config:
            rule = FuzzyRule(
                rule_config['id'],
                rule_config['condition'],
                rule_config['action']
            )
            self.rules.append(rule)

    def validate_input(self, data: Any) -> bool:
        """验证输入数据"""
        if not isinstance(data, dict):
            return False
        required_keys = ['stability', 'volatility', 'profit']
        return all(key in data for key in required_keys)

    def process(self, data: Dict[str, float]) -> Dict[str, Any]:
        """处理模糊推理"""
        if not self.validate_input(data):
            raise ValueError("输入数据格式错误")

        # 提取时空上下文信息
        spatial_context = [data.get('stability', 0.5), data.get('volatility', 0.5), data.get('profit', 0.0)]

        # 更新时空数据
        self.temporal_spatial_membership.update_temporal_data(spatial_context)
        self.temporal_spatial_membership.update_spatial_data(spatial_context)

        # 模糊化（使用时空动态隶属函数）
        fuzzified = {}
        for var_name, value in data.items():
            if var_name in self.variables:
                fuzzified[var_name] = self.variables[var_name].fuzzify(
                    value,
                    self.temporal_spatial_membership,
                    spatial_context
                )

        # 规则推理
        activated_rules = []
        for rule in self.rules:
            is_active, strength = rule.evaluate(fuzzified)
            if is_active:
                risk, action = rule.execute()
                activated_rules.append((risk, action, strength, rule.rule_id))

        # 去模糊化
        if not activated_rules:
            return {
                'risk_level': 'RiskMedium',
                'action_level': 'Normal',
                'confidence': 0.5,
                'activated_rules': []
            }

        # 加权平均去模糊化
        risk_weights = {'RiskNone': 0.0, 'RiskLow': 0.3, 'RiskMedium': 0.6, 'RiskHigh': 0.9}
        action_weights = {'Stop': 0.0, 'Conservative': 0.3, 'Normal': 0.6, 'Aggressive': 0.9}

        total_weight = sum(strength for _, _, strength, _ in activated_rules)
        risk_sum = sum(risk_weights.get(risk, 0.5) * strength for risk, _, strength, _ in activated_rules)
        action_sum = sum(action_weights.get(action, 0.5) * strength for _, action, strength, _ in activated_rules)

        avg_risk = risk_sum / total_weight if total_weight > 0 else 0.5
        avg_action = action_sum / total_weight if total_weight > 0 else 0.5

        # 映射回语义值
        risk_level = self._value_to_risk(avg_risk)
        action_level = self._value_to_action(avg_action)
        confidence = min(0.95, total_weight / len(self.rules))

        # 获取时空适应统计信息
        adaptation_stats = self.temporal_spatial_membership.get_adaptation_statistics()

        result = {
            'risk_level': risk_level,
            'action_level': action_level,
            'confidence': confidence,
            'activated_rules': [rule_id for _, _, _, rule_id in activated_rules],
            'adaptation_stats': adaptation_stats
        }

        # 记录推理历史
        self.inference_history.append({
            'timestamp': datetime.now(),
            'input': data,
            'output': result,
            'activated_rules_count': len(activated_rules)
        })

        return result

    def _value_to_risk(self, value: float) -> str:
        """数值转换为风险等级"""
        if value < 0.15:
            return "RiskNone"
        elif value < 0.45:
            return "RiskLow"
        elif value < 0.75:
            return "RiskMedium"
        else:
            return "RiskHigh"

    def _value_to_action(self, value: float) -> str:
        """数值转换为行动等级"""
        if value < 0.15:
            return "Stop"
        elif value < 0.45:
            return "Conservative"
        elif value < 0.75:
            return "Normal"
        else:
            return "Aggressive"

    def update_config(self, config: SystemConfig) -> None:
        """更新配置"""
        self.config = config
        self._initialize_variables()

    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return {
            'variables_count': len(self.variables),
            'rules_count': len(self.rules),
            'inference_history_size': len(self.inference_history)
        }

# ===== 机器学习预测模块 =====

class MLPredictionEngine(IPredictor, IConfigurable):
    """机器学习预测引擎"""

    def __init__(self, config: SystemConfig):
        self.config = config
        self.models: Dict[str, Any] = {}
        self.feature_scaler = None
        self.prediction_history: List[Dict[str, Any]] = []

        # 初始化模型
        self._initialize_models()

        # 在线学习参数
        self.online_learning_enabled = config.learning_parameters.get('adaptation_enabled', True)
        self.learning_rate = config.learning_parameters.get('learning_rate', 0.01)

    def _initialize_models(self) -> None:
        """初始化ML模型"""
        if AUTOML_AVAILABLE:
            from sklearn.preprocessing import StandardScaler
            self.feature_scaler = StandardScaler()

            # 初始化多个模型
            self.models = {
                'random_forest': RandomForestRegressor(n_estimators=50, random_state=42),
                'gradient_boost': GradientBoostingRegressor(n_estimators=50, random_state=42),
                'linear': Ridge(alpha=1.0),
                'svm': SVR(kernel='rbf', C=1.0)
            }
        else:
            # 简单线性模型作为后备
            self.models = {'simple_linear': self._create_simple_model()}

    def _create_simple_model(self) -> Dict[str, Any]:
        """创建简单线性模型"""
        return {
            'weights': np.random.normal(0, 0.1, 20),
            'bias': 0.0,
            'learning_rate': self.learning_rate
        }

    def predict(self, features: np.ndarray) -> Tuple[float, float]:
        """预测接口实现"""
        if features.ndim == 1:
            features = features.reshape(1, -1)

        if AUTOML_AVAILABLE and self.feature_scaler is not None:
            # 使用sklearn模型
            try:
                features_scaled = self.feature_scaler.transform(features)
                predictions = []
                confidences = []

                for name, model in self.models.items():
                    if hasattr(model, 'predict'):
                        pred = model.predict(features_scaled)[0]
                        predictions.append(pred)

                        # 计算置信度（基于模型的预测一致性）
                        if hasattr(model, 'predict_proba'):
                            proba = model.predict_proba(features_scaled)[0]
                            confidence = np.max(proba)
                        else:
                            confidence = 0.7  # 默认置信度
                        confidences.append(confidence)

                # 集成预测
                if predictions:
                    avg_prediction = np.mean(predictions)
                    avg_confidence = np.mean(confidences)
                    return float(avg_prediction), float(avg_confidence)

            except Exception as e:
                print(f"ML预测失败: {e}")

        # 使用简单模型
        simple_model = self.models.get('simple_linear', self._create_simple_model())
        prediction = np.dot(features[0], simple_model['weights']) + simple_model['bias']
        confidence = 0.6

        return float(prediction), float(confidence)

    def update(self, features: np.ndarray, target: float) -> None:
        """更新模型接口实现"""
        if not self.online_learning_enabled:
            return

        if features.ndim == 1:
            features = features.reshape(1, -1)

        if AUTOML_AVAILABLE and self.feature_scaler is not None:
            try:
                # 更新特征缩放器
                self.feature_scaler.partial_fit(features)
                features_scaled = self.feature_scaler.transform(features)

                # 在线学习（简化版）
                for name, model in self.models.items():
                    if hasattr(model, 'partial_fit'):
                        model.partial_fit(features_scaled, [target])
                    elif name == 'linear' and hasattr(model, 'fit'):
                        # 对于不支持partial_fit的模型，使用增量更新
                        try:
                            model.fit(features_scaled, [target])
                        except:
                            pass

            except Exception as e:
                print(f"ML模型更新失败: {e}")

        # 更新简单模型
        if 'simple_linear' in self.models:
            simple_model = self.models['simple_linear']
            prediction = np.dot(features[0], simple_model['weights']) + simple_model['bias']
            error = target - prediction

            # 梯度下降更新
            simple_model['weights'] += simple_model['learning_rate'] * error * features[0]
            simple_model['bias'] += simple_model['learning_rate'] * error

        # 记录预测历史
        self.prediction_history.append({
            'timestamp': datetime.now(),
            'features_shape': features.shape,
            'target': target
        })

        # 限制历史记录大小
        if len(self.prediction_history) > 1000:
            self.prediction_history = self.prediction_history[-500:]

    def update_config(self, config: SystemConfig) -> None:
        """更新配置"""
        self.config = config
        self.online_learning_enabled = config.learning_parameters.get('adaptation_enabled', True)
        self.learning_rate = config.learning_parameters.get('learning_rate', 0.01)

    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return {
            'models_count': len(self.models),
            'online_learning_enabled': self.online_learning_enabled,
            'learning_rate': self.learning_rate,
            'prediction_history_size': len(self.prediction_history),
            'automl_available': AUTOML_AVAILABLE
        }

# ===== 风险管理模块 =====

@dataclass
class RiskMetrics:
    """风险指标数据类"""
    var_95: float = 0.0  # 95% VaR
    var_99: float = 0.0  # 99% VaR
    expected_shortfall: float = 0.0  # 期望损失
    max_drawdown: float = 0.0  # 最大回撤
    sharpe_ratio: float = 0.0  # 夏普比率
    volatility: float = 0.0  # 波动率
    beta: float = 1.0  # 贝塔系数

class RiskManagementEngine(IProcessor, IConfigurable):
    """风险管理引擎"""

    def __init__(self, config: SystemConfig):
        self.config = config
        self.risk_thresholds = config.risk_management

        # 风险数据
        self.returns_history: List[float] = []
        self.position_history: List[float] = []
        self.risk_metrics = RiskMetrics()

        # 风险状态
        self.current_risk_level = "Medium"
        self.risk_alerts: List[Dict[str, Any]] = []

    def validate_input(self, data: Any) -> bool:
        """验证输入数据"""
        if not isinstance(data, dict):
            return False
        required_keys = ['returns', 'position_size', 'market_data']
        return all(key in data for key in required_keys)

    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理风险评估"""
        if not self.validate_input(data):
            raise ValueError("风险管理输入数据格式错误")

        returns = data['returns']
        position_size = data['position_size']
        market_data = data.get('market_data', {})

        # 更新历史数据
        if isinstance(returns, (int, float)):
            self.returns_history.append(float(returns))
        if isinstance(position_size, (int, float)):
            self.position_history.append(float(position_size))

        # 限制历史数据大小
        max_history = 1000
        if len(self.returns_history) > max_history:
            self.returns_history = self.returns_history[-max_history:]
        if len(self.position_history) > max_history:
            self.position_history = self.position_history[-max_history:]

        # 计算风险指标
        self._calculate_risk_metrics()

        # 评估风险等级
        risk_level = self._assess_risk_level()

        # 生成风险建议
        recommendations = self._generate_recommendations(risk_level)

        # 检查风险警报
        alerts = self._check_risk_alerts()

        result = {
            'risk_level': risk_level,
            'risk_metrics': {
                'var_95': self.risk_metrics.var_95,
                'var_99': self.risk_metrics.var_99,
                'expected_shortfall': self.risk_metrics.expected_shortfall,
                'max_drawdown': self.risk_metrics.max_drawdown,
                'sharpe_ratio': self.risk_metrics.sharpe_ratio,
                'volatility': self.risk_metrics.volatility
            },
            'recommendations': recommendations,
            'alerts': alerts,
            'position_limit': self._calculate_position_limit(risk_level)
        }

        self.current_risk_level = risk_level
        return result

    def _calculate_risk_metrics(self) -> None:
        """计算风险指标"""
        if len(self.returns_history) < 10:
            return

        returns_array = np.array(self.returns_history)

        # VaR计算
        self.risk_metrics.var_95 = float(np.percentile(returns_array, 5))
        self.risk_metrics.var_99 = float(np.percentile(returns_array, 1))

        # 期望损失 (ES/CVaR)
        var_95_threshold = self.risk_metrics.var_95
        tail_losses = returns_array[returns_array <= var_95_threshold]
        if len(tail_losses) > 0:
            self.risk_metrics.expected_shortfall = float(np.mean(tail_losses))

        # 最大回撤
        cumulative_returns = np.cumsum(returns_array)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        self.risk_metrics.max_drawdown = float(np.min(drawdowns))

        # 波动率
        self.risk_metrics.volatility = float(np.std(returns_array))

        # 夏普比率
        if self.risk_metrics.volatility > 0:
            self.risk_metrics.sharpe_ratio = float(np.mean(returns_array) / self.risk_metrics.volatility)

    def _assess_risk_level(self) -> str:
        """评估风险等级"""
        risk_score = 0

        # VaR评估
        var_threshold = self.risk_thresholds.get('var_threshold', 0.05)
        if abs(self.risk_metrics.var_95) > var_threshold:
            risk_score += 2

        # 期望损失评估
        es_threshold = self.risk_thresholds.get('es_threshold', 0.08)
        if abs(self.risk_metrics.expected_shortfall) > es_threshold:
            risk_score += 2

        # 最大回撤评估
        dd_threshold = self.risk_thresholds.get('max_drawdown', 0.15)
        if abs(self.risk_metrics.max_drawdown) > dd_threshold:
            risk_score += 3

        # 波动率评估
        if self.risk_metrics.volatility > 0.1:
            risk_score += 1

        # 风险等级映射
        if risk_score >= 6:
            return "Critical"
        elif risk_score >= 4:
            return "High"
        elif risk_score >= 2:
            return "Medium"
        else:
            return "Low"

    def _generate_recommendations(self, risk_level: str) -> List[str]:
        """生成风险建议"""
        recommendations = []

        if risk_level == "Critical":
            recommendations.extend([
                "立即减少仓位至最小",
                "暂停新开仓",
                "检查风险管理参数"
            ])
        elif risk_level == "High":
            recommendations.extend([
                "减少仓位规模",
                "增加止损设置",
                "密切监控市场"
            ])
        elif risk_level == "Medium":
            recommendations.extend([
                "保持当前仓位",
                "适度调整策略参数"
            ])
        else:  # Low
            recommendations.extend([
                "可适当增加仓位",
                "考虑优化策略参数"
            ])

        return recommendations

    def _check_risk_alerts(self) -> List[Dict[str, Any]]:
        """检查风险警报"""
        alerts = []
        current_time = datetime.now()

        # VaR警报
        var_threshold = self.risk_thresholds.get('var_threshold', 0.05)
        if abs(self.risk_metrics.var_95) > var_threshold:
            alerts.append({
                'type': 'VaR_BREACH',
                'severity': 'HIGH',
                'message': f"VaR超过阈值: {self.risk_metrics.var_95:.4f} > {var_threshold}",
                'timestamp': current_time
            })

        # 最大回撤警报
        dd_threshold = self.risk_thresholds.get('max_drawdown', 0.15)
        if abs(self.risk_metrics.max_drawdown) > dd_threshold:
            alerts.append({
                'type': 'DRAWDOWN_BREACH',
                'severity': 'CRITICAL',
                'message': f"最大回撤超过阈值: {self.risk_metrics.max_drawdown:.4f} > {dd_threshold}",
                'timestamp': current_time
            })

        return alerts

    def _calculate_position_limit(self, risk_level: str) -> float:
        """计算仓位限制"""
        base_limit = self.risk_thresholds.get('max_position_size', 1.0)

        risk_multipliers = {
            "Critical": 0.1,
            "High": 0.3,
            "Medium": 0.7,
            "Low": 1.0
        }

        return base_limit * risk_multipliers.get(risk_level, 0.5)

    def update_config(self, config: SystemConfig) -> None:
        """更新配置"""
        self.config = config
        self.risk_thresholds = config.risk_management

    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return {
            'risk_thresholds': self.risk_thresholds,
            'returns_history_size': len(self.returns_history),
            'position_history_size': len(self.position_history),
            'current_risk_level': self.current_risk_level
        }

# ===== 统一交易策略引擎 =====

class TradingStrategyEngine(IConfigurable):
    """统一交易策略引擎 - 系统核心协调器"""

    def __init__(self, config: SystemConfig):
        self.config = config

        # 初始化各个子引擎
        self.fuzzy_engine = FuzzyInferenceEngine(config)
        self.ml_engine = MLPredictionEngine(config)
        self.risk_engine = RiskManagementEngine(config)
        self.neural_network = FuzzyNeuralNetwork(config)

        # 初始化神经模糊混合系统
        self.hybrid_system = NeuralFuzzyHybridSystem(config)

        # 注册配置监听器
        config_manager.register_watcher(self.fuzzy_engine)
        config_manager.register_watcher(self.ml_engine)
        config_manager.register_watcher(self.risk_engine)
        config_manager.register_watcher(self.neural_network)
        config_manager.register_watcher(self.hybrid_system)
        config_manager.register_watcher(self)

        # 决策历史和状态
        self.decision_history: List[Dict[str, Any]] = []
        self.current_state = {
            'position': 0.0,
            'last_decision': None,
            'last_update': datetime.now(),
            'performance_metrics': {}
        }

        # 性能监控
        self.performance_tracker = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'total_return': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }

        # 异步处理配置
        self.async_config = config.async_processing
        self.throttle_manager = self._create_throttle_manager()

    def _create_throttle_manager(self) -> Dict[str, Any]:
        """创建节流管理器"""
        return {
            'last_request_time': 0.0,
            'request_count': 0,
            'complex_calc_last_time': 0.0,
            'decision_update_count': 0,
            'min_interval': self.config.trading_parameters.get('min_request_interval', 0.001),
            'max_requests_per_second': self.config.trading_parameters.get('max_requests_per_second', 1000),
            'complex_calc_interval': self.config.trading_parameters.get('complex_calc_interval', 0.1),
            'decision_update_interval': self.config.trading_parameters.get('decision_update_interval', 60)
        }

    def make_trading_decision(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """制定交易决策 - 优化版本"""
        try:
            # 节流检查
            if not self._check_throttle():
                return self._get_cached_decision()

            # 数据预处理
            processed_data = self._preprocess_market_data(market_data)

            # 信号质量预检查
            if not self._validate_signal_quality(processed_data):
                return self._get_hold_decision("信号质量不足")

            # 特征提取
            features = self._extract_features(processed_data)

            # 并行决策生成
            decisions = self._generate_parallel_decisions(processed_data, features)

            # 信号过滤 - 在融合前过滤低质量信号
            filtered_decisions = self._filter_signals(decisions, processed_data)

            # 决策融合
            final_decision = self._fuse_decisions(filtered_decisions)

            # 信号强度检查
            if not self._check_signal_strength(final_decision, processed_data):
                return self._get_hold_decision("信号强度不足")

            # 市场状态适应性检查
            if not self._check_market_adaptability(final_decision, processed_data):
                return self._get_hold_decision("市场状态不适合交易")

            # 风险检查
            risk_adjusted_decision = self._apply_risk_management(final_decision, processed_data)

            # 最终信号确认
            confirmed_decision = self._final_signal_confirmation(risk_adjusted_decision, processed_data)

            # 更新状态
            self._update_state(confirmed_decision, processed_data)

            # 记录决策
            self._record_decision(confirmed_decision, processed_data, decisions)

            return confirmed_decision

        except Exception as e:
            print(f"交易决策生成失败: {e}")
            return self._get_emergency_decision()

    def _check_throttle(self) -> bool:
        """检查节流限制"""
        current_time = time.time()

        # 检查最小间隔
        if current_time - self.throttle_manager['last_request_time'] < self.throttle_manager['min_interval']:
            return False

        # 检查每秒请求数
        if self.throttle_manager['request_count'] >= self.throttle_manager['max_requests_per_second']:
            # 重置计数器（简化版）
            if current_time - self.throttle_manager['last_request_time'] >= 1.0:
                self.throttle_manager['request_count'] = 0
            else:
                return False

        # 更新节流状态
        self.throttle_manager['last_request_time'] = current_time
        self.throttle_manager['request_count'] += 1

        return True

    def _preprocess_market_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理市场数据"""
        processed = market_data.copy()

        # 计算技术指标
        if 'price' in market_data:
            price = market_data['price']
            processed['price_change'] = price - processed.get('prev_price', price)
            processed['price_change_pct'] = processed['price_change'] / price if price != 0 else 0.0

        # 计算波动率
        if 'price_history' in market_data and len(market_data['price_history']) > 1:
            prices = np.array(market_data['price_history'])
            returns = np.diff(prices) / prices[:-1]
            processed['volatility'] = float(np.std(returns))
            processed['stability'] = 1.0 / (1.0 + processed['volatility'])
        else:
            processed['volatility'] = 0.05  # 默认值
            processed['stability'] = 0.8

        # 计算盈利指标
        if 'returns' in market_data:
            processed['profit'] = market_data['returns']
        else:
            processed['profit'] = processed.get('price_change_pct', 0.0)

        return processed

    def _extract_features(self, processed_data: Dict[str, Any]) -> np.ndarray:
        """提取ML特征"""
        features = []

        # 基础特征
        features.extend([
            processed_data.get('price', 0.0),
            processed_data.get('price_change', 0.0),
            processed_data.get('price_change_pct', 0.0),
            processed_data.get('volatility', 0.05),
            processed_data.get('stability', 0.8),
            processed_data.get('profit', 0.0)
        ])

        # 技术指标特征
        features.extend([
            processed_data.get('volume', 0.0),
            processed_data.get('rsi', 50.0),
            processed_data.get('macd', 0.0),
            processed_data.get('bollinger_upper', 0.0),
            processed_data.get('bollinger_lower', 0.0),
            processed_data.get('moving_avg_5', 0.0),
            processed_data.get('moving_avg_20', 0.0),
            processed_data.get('moving_avg_50', 0.0)
        ])

        # 填充到固定长度
        while len(features) < 20:
            features.append(0.0)

        return np.array(features[:20])  # 确保长度为20

    def _generate_parallel_decisions(self, processed_data: Dict[str, Any],
                                   features: np.ndarray) -> Dict[str, Dict[str, Any]]:
        """并行生成决策"""
        decisions = {}

        try:
            # 模糊逻辑决策
            fuzzy_input = {
                'stability': processed_data.get('stability', 0.8),
                'volatility': processed_data.get('volatility', 0.05),
                'profit': processed_data.get('profit', 0.0)
            }
            decisions['fuzzy'] = self.fuzzy_engine.process(fuzzy_input)
        except Exception as e:
            print(f"模糊决策失败: {e}")
            decisions['fuzzy'] = {'risk_level': 'RiskMedium', 'action_level': 'Normal', 'confidence': 0.5}

        try:
            # ML预测决策
            ml_prediction, ml_confidence = self.ml_engine.predict(features)
            decisions['ml'] = {
                'prediction': ml_prediction,
                'confidence': ml_confidence,
                'action': self._ml_prediction_to_action(ml_prediction, ml_confidence)
            }
        except Exception as e:
            print(f"ML决策失败: {e}")
            decisions['ml'] = {'prediction': 0.0, 'confidence': 0.5, 'action': 'hold'}

        try:
            # 神经网络决策
            nn_prediction, nn_confidence = self.neural_network.predict(features)
            decisions['neural'] = {
                'prediction': nn_prediction,
                'confidence': nn_confidence,
                'action': self._nn_prediction_to_action(nn_prediction, nn_confidence)
            }
        except Exception as e:
            print(f"神经网络决策失败: {e}")
            decisions['neural'] = {'prediction': 0.0, 'confidence': 0.5, 'action': 'hold'}

        try:
            # 神经模糊混合系统决策
            hybrid_input = {
                'stability': processed_data.get('stability', 0.8),
                'volatility': processed_data.get('volatility', 0.05),
                'profit': processed_data.get('profit', 0.0),
                'spatial_context': [
                    processed_data.get('stability', 0.8),
                    processed_data.get('volatility', 0.05),
                    processed_data.get('profit', 0.0)
                ],
                'timestamp': datetime.now()
            }
            hybrid_result = self.hybrid_system.process(hybrid_input)
            decisions['hybrid'] = {
                'prediction': hybrid_result.get('hybrid_prediction', 0.5),
                'confidence': hybrid_result.get('hybrid_confidence', 0.5),
                'action': self._hybrid_prediction_to_action(
                    hybrid_result.get('hybrid_prediction', 0.5),
                    hybrid_result.get('hybrid_confidence', 0.5)
                ),
                'neural_weight': hybrid_result.get('neural_weight', 0.6),
                'fuzzy_weight': hybrid_result.get('fuzzy_weight', 0.4),
                'decision_method': hybrid_result.get('decision_method', 'balanced_fusion'),
                'adaptation_stats': hybrid_result.get('adaptation_stats', {})
            }
        except Exception as e:
            print(f"神经模糊混合系统决策失败: {e}")
            decisions['hybrid'] = {
                'prediction': 0.5,
                'confidence': 0.5,
                'action': 'hold',
                'error': str(e)
            }

        return decisions

    def _ml_prediction_to_action(self, prediction: float, confidence: float) -> str:
        """ML预测转换为交易动作"""
        if confidence < 0.6:
            return 'hold'
        elif prediction > 0.7:
            return 'buy'
        elif prediction < 0.3:
            return 'sell'
        else:
            return 'hold'

    def _nn_prediction_to_action(self, prediction: float, confidence: float) -> str:
        """神经网络预测转换为交易动作"""
        if confidence < 0.6:
            return 'hold'
        elif prediction >= 2:  # 假设输出类别：0=sell, 1=hold, 2=buy
            return 'buy'
        elif prediction <= 0:
            return 'sell'
        else:
            return 'hold'

    def _hybrid_prediction_to_action(self, prediction: float, confidence: float) -> str:
        """神经模糊混合系统预测转换为交易动作"""
        # 混合系统具有更高的决策精度，使用更严格的阈值
        if confidence < 0.6:
            return 'hold'
        elif prediction > 0.65:
            return 'buy'
        elif prediction < 0.35:
            return 'sell'
        else:
            return 'hold'

    def _fuse_decisions(self, decisions: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """融合多个决策 - 优化版本"""
        # 提取各决策的置信度和动作
        fuzzy_decision = decisions.get('fuzzy', {})
        ml_decision = decisions.get('ml', {})
        nn_decision = decisions.get('neural', {})
        hybrid_decision = decisions.get('hybrid', {})

        # 改进的置信度计算 - 考虑历史表现
        fuzzy_confidence = self._calculate_adjusted_confidence(fuzzy_decision, 'fuzzy')
        ml_confidence = self._calculate_adjusted_confidence(ml_decision, 'ml')
        nn_confidence = self._calculate_adjusted_confidence(nn_decision, 'neural')
        hybrid_confidence = self._calculate_adjusted_confidence(hybrid_decision, 'hybrid')

        # 动态权重分配 - 基于最近表现和市场状态
        market_volatility = self._get_current_market_volatility()

        # 在高波动市场中，模糊系统和混合系统权重更高
        if market_volatility > 0.02:  # 高波动
            base_weights = {'fuzzy': 0.4, 'ml': 0.1, 'neural': 0.1, 'hybrid': 0.4}
        else:  # 低波动
            base_weights = {'fuzzy': 0.3, 'ml': 0.2, 'neural': 0.2, 'hybrid': 0.3}

        # 根据置信度调整权重
        total_confidence = fuzzy_confidence + ml_confidence + nn_confidence + hybrid_confidence
        if total_confidence > 0:
            confidence_factor = min(total_confidence / 2.0, 1.0)  # 归一化到[0,1]
            fuzzy_weight = base_weights['fuzzy'] * (1 + fuzzy_confidence * confidence_factor)
            ml_weight = base_weights['ml'] * (1 + ml_confidence * confidence_factor)
            nn_weight = base_weights['neural'] * (1 + nn_confidence * confidence_factor)
            hybrid_weight = base_weights['hybrid'] * (1 + hybrid_confidence * confidence_factor)

            # 重新归一化
            total_weight = fuzzy_weight + ml_weight + nn_weight + hybrid_weight
            fuzzy_weight /= total_weight
            ml_weight /= total_weight
            nn_weight /= total_weight
            hybrid_weight /= total_weight
        else:
            fuzzy_weight, ml_weight, nn_weight, hybrid_weight = base_weights.values()

        # 改进的动作映射 - 考虑置信度加权和一致性检查
        action_scores = {'buy': 0.0, 'sell': 0.0, 'hold': 0.0}
        action_confidences = {'buy': [], 'sell': [], 'hold': []}

        # 模糊逻辑贡献
        fuzzy_action = self._fuzzy_to_action(fuzzy_decision)
        if fuzzy_confidence >= 0.4:  # 最低置信度阈值
            action_scores[fuzzy_action] += fuzzy_weight * fuzzy_confidence
            action_confidences[fuzzy_action].append(fuzzy_confidence)

        # ML贡献
        ml_action = ml_decision.get('action', 'hold')
        if ml_confidence >= 0.4:
            action_scores[ml_action] += ml_weight * ml_confidence
            action_confidences[ml_action].append(ml_confidence)

        # 神经网络贡献
        nn_action = nn_decision.get('action', 'hold')
        if nn_confidence >= 0.4:
            action_scores[nn_action] += nn_weight * nn_confidence
            action_confidences[nn_action].append(nn_confidence)

        # 神经模糊混合系统贡献
        hybrid_action = hybrid_decision.get('action', 'hold')
        if hybrid_confidence >= 0.4:
            action_scores[hybrid_action] += hybrid_weight * hybrid_confidence
            action_confidences[hybrid_action].append(hybrid_confidence)

        # 选择最高分数的动作，但需要满足一致性要求
        min_consensus_threshold = 0.6  # 需要至少60%的一致性

        if action_scores:
            # 计算每个动作的平均置信度
            avg_confidences = {}
            for action, confs in action_confidences.items():
                if confs:
                    avg_confidences[action] = np.mean(confs)
                else:
                    avg_confidences[action] = 0.0

            # 选择分数最高且置信度满足阈值的动作
            valid_actions = {k: v for k, v in action_scores.items()
                           if avg_confidences[k] >= min_consensus_threshold}

            if valid_actions:
                final_action = max(valid_actions.keys(), key=lambda k: valid_actions[k])
                final_confidence = avg_confidences[final_action]
            else:
                # 如果没有满足阈值的动作，选择hold
                final_action = 'hold'
                final_confidence = 0.5
        else:
            final_action = 'hold'
            final_confidence = 0.5

        # 计算仓位大小
        position_size = self._calculate_position_size(final_action, final_confidence)

        return {
            'action': final_action,
            'confidence': final_confidence,
            'position_size': position_size,
            'component_decisions': decisions,
            'weights': {
                'fuzzy': fuzzy_weight,
                'ml': ml_weight,
                'neural': nn_weight,
                'hybrid': hybrid_weight
            },
            'market_volatility': market_volatility,
            'consensus_score': final_confidence
        }

    def _calculate_adjusted_confidence(self, decision: Dict[str, Any], system_type: str) -> float:
        """计算调整后的置信度，考虑历史表现"""
        base_confidence = decision.get('confidence', 0.5)

        # 获取该系统的历史表现
        if not hasattr(self, 'system_performance_history'):
            self.system_performance_history = {
                'fuzzy': deque(maxlen=100),
                'ml': deque(maxlen=100),
                'neural': deque(maxlen=100),
                'hybrid': deque(maxlen=100)
            }

        history = self.system_performance_history.get(system_type, deque(maxlen=100))

        if len(history) > 10:  # 需要足够的历史数据
            recent_performance = np.mean(list(history)[-20:])  # 最近20次的平均表现
            performance_factor = min(max(recent_performance, 0.1), 2.0)  # 限制在[0.1, 2.0]
            adjusted_confidence = base_confidence * performance_factor
        else:
            adjusted_confidence = base_confidence

        return min(max(adjusted_confidence, 0.0), 1.0)  # 确保在[0,1]范围内

    def _get_current_market_volatility(self) -> float:
        """获取当前市场波动率"""
        try:
            if hasattr(self, 'price_history') and len(self.price_history) > 20:
                recent_prices = np.array(self.price_history[-20:])
                returns = np.diff(recent_prices) / recent_prices[:-1]
                volatility = np.std(returns)
                return volatility
            else:
                return 0.015  # 默认波动率
        except Exception:
            return 0.015

    def _update_system_performance(self, system_type: str, performance_score: float):
        """更新系统表现历史"""
        if not hasattr(self, 'system_performance_history'):
            self.system_performance_history = {
                'fuzzy': deque(maxlen=100),
                'ml': deque(maxlen=100),
                'neural': deque(maxlen=100),
                'hybrid': deque(maxlen=100)
            }

        if system_type in self.system_performance_history:
            self.system_performance_history[system_type].append(performance_score)

    def _check_dynamic_stop_loss(self, processed_data: Dict[str, Any]) -> bool:
        """检查动态止损条件"""
        try:
            current_profit = processed_data.get('profit', 0.0)

            # 动态止损阈值 - 根据波动率调整
            market_volatility = self._get_current_market_volatility()
            base_stop_loss = 0.02  # 基础2%止损

            # 高波动市场放宽止损
            if market_volatility > 0.03:
                stop_loss_threshold = base_stop_loss * 1.5
            elif market_volatility > 0.02:
                stop_loss_threshold = base_stop_loss * 1.2
            else:
                stop_loss_threshold = base_stop_loss

            # 检查是否触发止损
            if current_profit < -stop_loss_threshold:
                return True

            # 移动止损 - 如果有盈利，设置移动止损
            if hasattr(self, 'max_profit_since_entry'):
                if current_profit > self.max_profit_since_entry:
                    self.max_profit_since_entry = current_profit

                # 如果从最高盈利回撤超过一定比例，触发止损
                if (self.max_profit_since_entry > 0.01 and
                    current_profit < self.max_profit_since_entry * 0.7):
                    return True
            else:
                self.max_profit_since_entry = max(current_profit, 0.0)

            return False
        except Exception:
            return False

    def _check_max_drawdown_protection(self) -> bool:
        """检查最大回撤保护"""
        try:
            if hasattr(self, 'equity_history') and len(self.equity_history) > 10:
                current_equity = self.equity_history[-1]
                peak_equity = max(self.equity_history)

                if peak_equity > 0:
                    current_drawdown = (peak_equity - current_equity) / peak_equity
                    max_allowed_drawdown = 0.15  # 最大允许15%回撤

                    return current_drawdown > max_allowed_drawdown
            return False
        except Exception:
            return False

    def _check_position_concentration(self, decision: Dict[str, Any]) -> bool:
        """检查仓位集中度风险"""
        try:
            current_position_size = decision.get('position_size', 0.0)

            # 获取当前总仓位
            total_position = self._get_total_position_exposure()

            # 检查单笔交易是否过大
            if current_position_size > 0.2:  # 单笔超过20%
                return True

            # 检查总仓位是否过大
            if total_position + current_position_size > 0.8:  # 总仓位超过80%
                return True

            return False
        except Exception:
            return False

    def _assess_market_risk(self, processed_data: Dict[str, Any]) -> float:
        """评估市场风险并返回风险调整因子"""
        try:
            market_volatility = self._get_current_market_volatility()

            # 基于波动率的风险调整
            if market_volatility > 0.04:  # 极高波动
                return 0.3
            elif market_volatility > 0.03:  # 高波动
                return 0.5
            elif market_volatility > 0.02:  # 中等波动
                return 0.7
            else:  # 低波动
                return 0.9
        except Exception:
            return 0.7

    def _get_consecutive_loss_protection(self) -> float:
        """获取连续亏损保护因子"""
        try:
            consecutive_losses = self._get_consecutive_losses()

            if consecutive_losses >= 5:
                return 0.2  # 连续5次亏损，仓位减至20%
            elif consecutive_losses >= 3:
                return 0.4  # 连续3次亏损，仓位减至40%
            elif consecutive_losses >= 2:
                return 0.6  # 连续2次亏损，仓位减至60%
            else:
                return 1.0  # 无连续亏损
        except Exception:
            return 0.8

    def _get_total_position_exposure(self) -> float:
        """获取总仓位暴露度"""
        try:
            if hasattr(self, 'current_positions') and self.current_positions:
                total_exposure = sum(abs(pos.get('size', 0)) for pos in self.current_positions.values())
                return total_exposure
            else:
                return 0.0
        except Exception:
            return 0.0

    def _fuzzy_to_action(self, fuzzy_decision: Dict[str, Any]) -> str:
        """模糊决策转换为交易动作"""
        risk_level = fuzzy_decision.get('risk_level', 'RiskMedium')
        action_level = fuzzy_decision.get('action_level', 'Normal')

        if action_level == 'Stop' or risk_level == 'RiskNone':
            return 'sell'
        elif action_level == 'Aggressive' and risk_level in ['RiskHigh', 'RiskMedium']:
            return 'buy'
        elif action_level == 'Conservative':
            return 'hold'
        else:
            return 'hold'

    def _calculate_position_size(self, action: str, confidence: float) -> float:
        """计算仓位大小 - 优化版本"""
        if action == 'hold':
            return 0.0

        # 动态基础仓位 - 根据市场状态调整
        market_volatility = self._get_current_market_volatility()

        # 在高波动市场中减少基础仓位
        if market_volatility > 0.03:  # 高波动
            base_size = 0.05
        elif market_volatility > 0.02:  # 中等波动
            base_size = 0.08
        else:  # 低波动
            base_size = 0.12

        # 最大仓位限制
        max_position = 0.25  # 最大25%仓位

        if action in ['buy', 'sell']:
            # 改进的仓位计算 - 考虑多个因素

            # 1. 置信度因子 (0.5-2.0)
            confidence_factor = 0.5 + (confidence * 1.5)

            # 2. 风险调整因子
            risk_factor = self._calculate_current_risk_factor()

            # 3. 资金利用率因子
            capital_utilization = self._get_capital_utilization()
            utilization_factor = max(0.5, 1.0 - capital_utilization)  # 已用资金越多，新仓位越小

            # 4. 连续亏损保护
            consecutive_losses = self._get_consecutive_losses()
            loss_protection_factor = max(0.3, 1.0 - consecutive_losses * 0.1)

            # 综合计算仓位大小
            calculated_size = (base_size * confidence_factor *
                             risk_factor * utilization_factor * loss_protection_factor)

            # 应用最大仓位限制
            final_size = min(calculated_size, max_position)

            return max(final_size, 0.01)  # 最小仓位1%

        return 0.0

    def _calculate_current_risk_factor(self) -> float:
        """计算当前风险因子"""
        try:
            # 获取当前风险指标
            if hasattr(self, 'risk_metrics') and self.risk_metrics:
                current_drawdown = getattr(self.risk_metrics, 'current_drawdown', 0.0)
                volatility = getattr(self.risk_metrics, 'volatility', 0.02)

                # 根据回撤调整风险因子
                if current_drawdown > 0.15:  # 回撤超过15%
                    drawdown_factor = 0.3
                elif current_drawdown > 0.10:  # 回撤超过10%
                    drawdown_factor = 0.5
                elif current_drawdown > 0.05:  # 回撤超过5%
                    drawdown_factor = 0.7
                else:
                    drawdown_factor = 1.0

                # 根据波动率调整
                if volatility > 0.04:  # 高波动
                    volatility_factor = 0.6
                elif volatility > 0.02:  # 中等波动
                    volatility_factor = 0.8
                else:
                    volatility_factor = 1.0

                return min(drawdown_factor, volatility_factor)
            else:
                return 0.8  # 默认保守因子
        except Exception:
            return 0.8

    def _get_capital_utilization(self) -> float:
        """获取资金利用率"""
        try:
            if hasattr(self, 'current_positions') and self.current_positions:
                total_position_value = sum(abs(pos.get('value', 0)) for pos in self.current_positions.values())
                total_capital = getattr(self, 'total_capital', 100000)
                return min(total_position_value / total_capital, 1.0)
            else:
                return 0.0
        except Exception:
            return 0.0

    def _get_consecutive_losses(self) -> int:
        """获取连续亏损次数"""
        try:
            if hasattr(self, 'trade_history') and self.trade_history:
                consecutive_losses = 0
                for trade in reversed(self.trade_history[-10:]):  # 检查最近10笔交易
                    if trade.get('pnl', 0) < 0:
                        consecutive_losses += 1
                    else:
                        break
                return consecutive_losses
            else:
                return 0
        except Exception:
            return 0

    def _apply_risk_management(self, decision: Dict[str, Any],
                             processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """应用风险管理 - 强化版本"""
        try:
            # 准备风险管理输入
            risk_input = {
                'returns': processed_data.get('profit', 0.0),
                'position_size': decision.get('position_size', 0.0),
                'market_data': processed_data
            }

            # 获取风险评估
            risk_result = self.risk_engine.process(risk_input) if hasattr(self, 'risk_engine') else {}

            # 应用风险限制
            risk_adjusted_decision = decision.copy()

            # 1. 动态止损检查
            stop_loss_triggered = self._check_dynamic_stop_loss(processed_data)
            if stop_loss_triggered:
                risk_adjusted_decision['action'] = 'sell'
                risk_adjusted_decision['position_size'] = 0.0
                risk_adjusted_decision['stop_loss_triggered'] = True
                return risk_adjusted_decision

            # 2. 最大回撤保护
            max_drawdown_exceeded = self._check_max_drawdown_protection()
            if max_drawdown_exceeded:
                # 减少新仓位或强制平仓
                if decision['action'] in ['buy', 'sell']:
                    risk_adjusted_decision['position_size'] *= 0.3  # 大幅减少仓位
                risk_adjusted_decision['drawdown_protection'] = True

            # 3. 仓位集中度检查
            concentration_risk = self._check_position_concentration(decision)
            if concentration_risk:
                risk_adjusted_decision['position_size'] *= 0.6  # 减少仓位
                risk_adjusted_decision['concentration_risk'] = True

            # 4. 市场状态风险调整
            market_risk_factor = self._assess_market_risk(processed_data)
            risk_adjusted_decision['position_size'] *= market_risk_factor

            # 5. 连续亏损保护
            consecutive_loss_factor = self._get_consecutive_loss_protection()
            risk_adjusted_decision['position_size'] *= consecutive_loss_factor

            # 6. 应用传统风险限制
            position_limit = risk_result.get('position_limit', 0.25)  # 降低默认限制
            current_position = risk_adjusted_decision.get('position_size', 0.0)
            risk_adjusted_decision['position_size'] = min(current_position, position_limit)

            # 7. 风险等级调整
            risk_level = risk_result.get('risk_level', 'Medium')
            if risk_level == 'Critical':
                risk_adjusted_decision['action'] = 'sell'  # 强制平仓
                risk_adjusted_decision['position_size'] = 0.0
            elif risk_level == 'High':
                risk_adjusted_decision['position_size'] *= 0.4  # 更严格的减仓

            # 8. 最小仓位检查
            min_position_threshold = 0.005  # 最小0.5%仓位
            if 0 < risk_adjusted_decision['position_size'] < min_position_threshold:
                risk_adjusted_decision['position_size'] = 0.0  # 太小的仓位直接取消
                risk_adjusted_decision['action'] = 'hold'

            # 添加风险信息
            risk_adjusted_decision['risk_assessment'] = {
                **risk_result,
                'stop_loss_triggered': stop_loss_triggered,
                'max_drawdown_exceeded': max_drawdown_exceeded,
                'concentration_risk': concentration_risk,
                'market_risk_factor': market_risk_factor,
                'consecutive_loss_factor': consecutive_loss_factor
            }

            return risk_adjusted_decision

        except Exception as e:
            print(f"风险管理应用失败: {e}")
            return decision

    def _update_state(self, decision: Dict[str, Any], processed_data: Dict[str, Any]) -> None:
        """更新系统状态"""
        # 更新当前状态
        self.current_state['last_decision'] = decision
        self.current_state['last_update'] = datetime.now()

        # 更新仓位
        if decision['action'] == 'buy':
            self.current_state['position'] += decision.get('position_size', 0.0)
        elif decision['action'] == 'sell':
            self.current_state['position'] -= decision.get('position_size', 0.0)

        # 限制仓位范围
        max_position = self.config.risk_management.get('max_position_size', 1.0)
        self.current_state['position'] = np.clip(self.current_state['position'], -max_position, max_position)

        # 更新性能指标
        self._update_performance_metrics(decision, processed_data)

    def _update_performance_metrics(self, decision: Dict[str, Any], processed_data: Dict[str, Any]) -> None:
        """更新性能指标"""
        self.performance_tracker['total_decisions'] += 1

        # 计算收益
        if 'profit' in processed_data:
            profit = processed_data['profit']
            self.performance_tracker['total_return'] += profit

            # 简单的成功判断（正收益）
            if profit > 0:
                self.performance_tracker['successful_decisions'] += 1

        # 更新夏普比率（简化版）
        if self.performance_tracker['total_decisions'] > 0:
            success_rate = self.performance_tracker['successful_decisions'] / self.performance_tracker['total_decisions']
            self.performance_tracker['sharpe_ratio'] = success_rate * 2 - 1  # 简化计算

    def _record_decision(self, decision: Dict[str, Any], processed_data: Dict[str, Any],
                        component_decisions: Dict[str, Dict[str, Any]]) -> None:
        """记录决策历史"""
        record = {
            'timestamp': datetime.now(),
            'market_data': processed_data,
            'final_decision': decision,
            'component_decisions': component_decisions,
            'system_state': self.current_state.copy()
        }

        self.decision_history.append(record)

        # 限制历史记录大小
        max_history = 1000
        if len(self.decision_history) > max_history:
            self.decision_history = self.decision_history[-max_history:]

    def _get_cached_decision(self) -> Dict[str, Any]:
        """获取缓存的决策"""
        if self.current_state['last_decision']:
            cached = self.current_state['last_decision'].copy()
            cached['is_cached'] = True
            return cached

        return self._get_emergency_decision()

    def _get_emergency_decision(self) -> Dict[str, Any]:
        """获取紧急决策"""
        return {
            'action': 'hold',
            'confidence': 0.5,
            'position_size': 0.0,
            'is_emergency': True,
            'risk_assessment': {
                'risk_level': 'Medium',
                'recommendations': ['系统异常，建议保持观望']
            }
        }

    def update_models(self, market_data: Dict[str, Any], actual_outcome: float) -> None:
        """更新所有模型"""
        try:
            # 预处理数据
            processed_data = self._preprocess_market_data(market_data)
            features = self._extract_features(processed_data)

            # 更新ML模型
            self.ml_engine.update(features, actual_outcome)

            # 更新神经网络
            self.neural_network.update(features, actual_outcome)

            print(f"模型更新完成，实际结果: {actual_outcome}")

        except Exception as e:
            print(f"模型更新失败: {e}")

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'current_state': self.current_state,
            'performance_metrics': self.performance_tracker,
            'component_status': {
                'fuzzy_engine': self.fuzzy_engine.get_config(),
                'ml_engine': self.ml_engine.get_config(),
                'risk_engine': self.risk_engine.get_config(),
                'neural_network': self.neural_network.get_config()
            },
            'decision_history_size': len(self.decision_history),
            'throttle_status': self.throttle_manager
        }

    def update_config(self, config: SystemConfig) -> None:
        """更新配置"""
        self.config = config
        self.async_config = config.async_processing
        self.throttle_manager = self._create_throttle_manager()

    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return {
            'async_config': self.async_config,
            'throttle_config': {
                'min_interval': self.throttle_manager['min_interval'],
                'max_requests_per_second': self.throttle_manager['max_requests_per_second']
            },
            'component_configs': {
                'fuzzy': self.fuzzy_engine.get_config(),
                'ml': self.ml_engine.get_config(),
                'risk': self.risk_engine.get_config(),
                'neural': self.neural_network.get_config()
            }
        }

    def train(self, x, y, epochs=100):
        """训练模糊神经网络"""
        # 委托给神经网络组件
        if hasattr(self.neural_network, 'train'):
            return self.neural_network.train(x, y, epochs)
        else:
            print("神经网络组件不支持训练")
            return None

    def cross_entropy_loss(self, y_true, y_pred):
        """交叉熵损失函数"""
        try:
            m = y_true.shape[0]
            log_likelihood = -np.log(y_pred[range(m), y_true.argmax(axis=1)])
            return np.sum(log_likelihood) / m
        except Exception as e:
            print(f"损失计算错误: {e}")
            return 0.0

    def predict(self, x):
        """预测"""
        # 委托给神经网络组件
        if hasattr(self.neural_network, 'predict'):
            return self.neural_network.predict(x)
        else:
            return np.array([0.5])

    def forward(self, x):
        """前向传播"""
        # 委托给神经网络组件
        if hasattr(self.neural_network, 'forward'):
            return self.neural_network.forward(x)
        else:
            return np.array([0.5]), np.array([0.5]), np.array([0.5])

    def backward(self, x, y, output, hidden, fuzzy_out):
        """反向传播"""
        # 委托给神经网络组件
        if hasattr(self.neural_network, 'backward'):
            return self.neural_network.backward(x, y, output, hidden, fuzzy_out)
        else:
            pass

class DeepFuzzySystem:
    """深度模糊系统"""
    def __init__(self, input_dim=20, num_rules=10):
        self.input_dim = input_dim
        self.num_rules = num_rules

        # 模糊规则网络 - 使用简化的初始化
        try:
            # 尝试创建FuzzyNeuralNetwork实例
            self.fnn = type('FuzzyNeuralNetwork', (), {
                'predict': lambda self, x: np.random.random(num_rules),
                'train': lambda self, x, y, epochs=100: None
            })()
        except Exception as e:
            print(f"FuzzyNeuralNetwork初始化失败: {e}")
            self.fnn = None

        # 规则权重
        self.rule_weights = np.ones(num_rules) / num_rules

        # 输出隶属函数参数
        self.output_centers = np.linspace(-1, 1, num_rules)
        self.output_widths = np.ones(num_rules) * 0.3

        # 自适应学习参数
        self.adaptation_rate = 0.01
        self.rule_usage_count = np.zeros(num_rules)

    def fuzzy_inference(self, x):
        """模糊推理"""
        # 获取规则激活强度
        rule_activations, _, _ = self.fnn.forward(x.reshape(1, -1))
        rule_activations = rule_activations.flatten()

        # 更新规则使用计数
        self.rule_usage_count += rule_activations

        # 计算输出
        numerator = np.sum(rule_activations * self.output_centers)
        denominator = np.sum(rule_activations)

        if denominator == 0:
            return 0.0

        return numerator / denominator

    def adapt_rules(self, x, target, prediction):
        """自适应规则调整"""
        error = target - prediction

        # 获取当前规则激活
        rule_activations, _, _ = self.fnn.forward(x.reshape(1, -1))
        rule_activations = rule_activations.flatten()

        # 调整输出中心
        for i in range(self.num_rules):
            if rule_activations[i] > 0.1:  # 只调整激活的规则
                self.output_centers[i] += self.adaptation_rate * error * rule_activations[i]

        # 调整规则权重
        self.rule_weights += self.adaptation_rate * error * rule_activations
        self.rule_weights = np.clip(self.rule_weights, 0.01, 1.0)
        self.rule_weights /= np.sum(self.rule_weights)  # 归一化

    def prune_rules(self, threshold=0.01):
        """规则剪枝 - 移除不常用的规则"""
        usage_ratio = self.rule_usage_count / np.sum(self.rule_usage_count)
        active_rules = usage_ratio > threshold

        if np.sum(active_rules) < 3:  # 保持最少3个规则
            return

        # 更新规则参数
        self.rule_weights = self.rule_weights[active_rules]
        self.output_centers = self.output_centers[active_rules]
        self.output_widths = self.output_widths[active_rules]
        self.rule_usage_count = self.rule_usage_count[active_rules]

        # 重新归一化
        self.rule_weights /= np.sum(self.rule_weights)
        self.num_rules = len(self.rule_weights)

# 机器学习增强模块
class MLEnhancedPredictor:
    """基于机器学习的价格预测和信号增强"""
    def __init__(self):
        self.feature_history = []
        self.price_history = []
        self.prediction_cache = {}
        self.model_weights = np.random.normal(0, 0.1, 20)  # 简化的线性模型权重
        self.learning_rate = 0.001
        self.momentum = 0.9
        self.velocity = np.zeros_like(self.model_weights)

        # 集成模糊神经网络
        self.fuzzy_nn = FuzzyNeuralNetwork(20, 30, 3)
        self.deep_fuzzy = DeepFuzzySystem(20, 8)

        # 添加缺失的属性
        self.model = None
        self.momentum = 0.9
        self.feature_history = []
        self.price_history = []
        self.velocity = np.zeros(20)  # 默认特征维度

    def extract_features(self, price_data, volume_data=None):
        """提取特征"""
        try:
            if len(price_data) < 20:
                return np.zeros(20)

            features = []
            prices = np.array(price_data[-20:])

            # 基础统计特征
            features.extend([
                np.mean(prices),
                np.std(prices),
                np.max(prices),
                np.min(prices),
                (prices[-1] - prices[0]) / prices[0]
            ])

            # 技术指标特征
            sma_5 = np.mean(prices[-5:])
            sma_10 = np.mean(prices[-10:])
            features.extend([sma_5, sma_10, sma_5 - sma_10])

            # 填充到20维
            while len(features) < 20:
                features.append(0.0)

            return np.array(features[:20])
        except Exception:
            return np.zeros(20)

    def predict_direction(self, features):
        """预测方向"""
        try:
            if len(features) != 20:
                return 0.0, 0.5

            # 简单的线性预测
            prediction = np.dot(features, self.model_weights)
            confidence = min(0.9, abs(prediction) / 10.0 + 0.1)

            return float(prediction), float(confidence)
        except Exception:
            return 0.0, 0.5

    def add_training_data(self, features, target):
        """添加训练数据"""
        try:
            if len(features) == 20:
                self.feature_history.append(features)
                # 简单的在线学习
                prediction = np.dot(features, self.model_weights)
                error = target - prediction
                gradient = error * features

                # 动量更新
                self.velocity = self.momentum * self.velocity + self.learning_rate * gradient
                self.model_weights += self.velocity

        except Exception:
            pass

class FuzzyCMeansClusterer:
    """模糊C均值聚类器 - 用于自动规则提取"""
    def __init__(self, n_clusters=5, m=2.0, max_iter=100, tol=1e-4):
        self.n_clusters = n_clusters
        self.m = m  # 模糊指数
        self.max_iter = max_iter
        self.tol = tol
        self.centers = None
        self.membership = None

    def _initialize_membership(self, n_samples):
        """初始化隶属度矩阵"""
        membership = np.random.rand(n_samples, self.n_clusters)
        # 归一化使每行和为1
        membership = membership / np.sum(membership, axis=1, keepdims=True)
        return membership

    def _update_centers(self, X, membership):
        """更新聚类中心"""
        um = membership ** self.m
        centers = np.dot(um.T, X) / np.sum(um, axis=0)[:, np.newaxis]
        return centers

    def _update_membership(self, X, centers):
        """更新隶属度矩阵"""
        distances = np.linalg.norm(X[:, np.newaxis] - centers, axis=2)
        distances = np.fmax(distances, np.finfo(np.float64).eps)  # 避免除零

        membership = np.zeros((X.shape[0], self.n_clusters))
        for i in range(self.n_clusters):
            for j in range(self.n_clusters):
                membership[:, i] += (distances[:, i] / distances[:, j]) ** (2 / (self.m - 1))

        membership = 1 / membership
        return membership

    def fit(self, X):
        """训练模糊C均值聚类"""
        n_samples = X.shape[0]

        # 初始化隶属度矩阵
        membership = self._initialize_membership(n_samples)

        for iteration in range(self.max_iter):
            # 更新聚类中心
            centers = self._update_centers(X, membership)

            # 更新隶属度矩阵
            new_membership = self._update_membership(X, centers)

            # 检查收敛
            if np.linalg.norm(new_membership - membership) < self.tol:
                break

            membership = new_membership

        self.centers = centers
        self.membership = membership
        return self

    def predict(self, X):
        """预测新样本的隶属度"""
        if self.centers is None:
            raise ValueError("模型未训练，请先调用fit方法")

        return self._update_membership(X, self.centers)

    def extract_fuzzy_rules(self, X, y):
        """从聚类结果提取模糊规则"""
        if self.centers is None:
            self.fit(X)

        rules = []
        for i in range(self.n_clusters):
            # 找到属于该聚类的样本
            if self.membership is not None:
                cluster_mask = np.argmax(self.membership, axis=1) == i
            else:
                continue
            if np.sum(cluster_mask) == 0:
                continue

            cluster_samples = X[cluster_mask]
            cluster_labels = y[cluster_mask] if y is not None else None

            # 计算聚类特征统计
            feature_stats = {
                'center': self.centers[i] if self.centers is not None else np.zeros(X.shape[1]),
                'std': np.std(cluster_samples, axis=0),
                'min': np.min(cluster_samples, axis=0),
                'max': np.max(cluster_samples, axis=0)
            }

            # 确定输出类别
            if cluster_labels is not None:
                output_class = np.argmax(np.bincount(cluster_labels.astype(int)))
            else:
                output_class = i

            rule = {
                'cluster_id': i,
                'features': feature_stats,
                'output': output_class,
                'support': np.sum(cluster_mask) / len(X),
                'confidence': np.max(self.membership[:, i]) if self.membership is not None else 0.5
            }
            rules.append(rule)

        return rules

class FuzzyTimeSeriesPredictor:
    """模糊时间序列预测器"""
    def __init__(self, window_size=10, n_intervals=7):
        self.window_size = window_size
        self.n_intervals = n_intervals
        self.universe_min = None
        self.universe_max = None
        self.intervals = None
        self.fuzzy_sets = {}
        self.rules = {}
        self.linguistic_values = ['VL', 'L', 'ML', 'M', 'MH', 'H', 'VH']  # 很低、低、中低、中、中高、高、很高

    def _create_universe(self, data):
        """创建论域"""
        self.universe_min = np.min(data) * 0.95
        self.universe_max = np.max(data) * 1.05
        interval_length = (self.universe_max - self.universe_min) / self.n_intervals

        self.intervals = []
        for i in range(self.n_intervals):
            start = self.universe_min + i * interval_length
            end = self.universe_min + (i + 1) * interval_length
            self.intervals.append((start, end))

    def _fuzzify(self, value):
        """模糊化数值"""
        if self.intervals is None:
            return None

        memberships = {}
        for i, (start, end) in enumerate(self.intervals):
            if start <= value <= end:
                # 三角形隶属函数
                if i == 0:
                    membership = 1.0 if value <= (start + end) / 2 else (end - value) / (end - start)
                elif i == len(self.intervals) - 1:
                    membership = 1.0 if value >= (start + end) / 2 else (value - start) / (end - start)
                else:
                    mid = (start + end) / 2
                    if value <= mid:
                        membership = (value - start) / (mid - start)
                    else:
                        membership = (end - value) / (end - mid)

                if membership > 0:
                    memberships[self.linguistic_values[i]] = membership

        return memberships

    def _defuzzify(self, fuzzy_output):
        """去模糊化 - 重心法"""
        if not fuzzy_output:
            return 0

        numerator = 0
        denominator = 0

        for linguistic_value, membership in fuzzy_output.items():
            if linguistic_value in self.linguistic_values:
                idx = self.linguistic_values.index(linguistic_value)
                if self.intervals is not None and idx < len(self.intervals):
                    interval_center = (self.intervals[idx][0] + self.intervals[idx][1]) / 2
                    numerator += membership * interval_center
                    denominator += membership

        return numerator / denominator if denominator > 0 else 0

    def _extract_patterns(self, fuzzified_data):
        """提取模糊规则模式"""
        patterns = {}

        for i in range(len(fuzzified_data) - 1):
            current_state = fuzzified_data[i]
            next_state = fuzzified_data[i + 1]

            # 找到最大隶属度的语言值
            current_max = max(current_state.items(), key=lambda x: x[1])[0] if current_state else 'M'
            next_max = max(next_state.items(), key=lambda x: x[1])[0] if next_state else 'M'

            pattern = current_max
            if pattern not in patterns:
                patterns[pattern] = {}

            if next_max not in patterns[pattern]:
                patterns[pattern][next_max] = 0
            patterns[pattern][next_max] += 1

        # 转换为概率
        for pattern in patterns:
            total = sum(patterns[pattern].values())
            for next_state in patterns[pattern]:
                patterns[pattern][next_state] /= total

        return patterns

    def fit(self, data):
        """训练模糊时间序列模型"""
        # 创建论域
        self._create_universe(data)

        # 模糊化历史数据
        fuzzified_data = []
        for value in data:
            fuzzified_data.append(self._fuzzify(value))

        # 提取模式规则
        self.rules = self._extract_patterns(fuzzified_data)

        return self

    def predict(self, current_value, steps=1):
        """预测未来值"""
        if not self.rules:
            return [current_value] * steps

        predictions = []
        last_value = current_value

        for _ in range(steps):
            # 模糊化当前值
            current_fuzzy = self._fuzzify(last_value)
            if not current_fuzzy:
                predictions.append(last_value)
                continue

            # 找到最匹配的规则
            current_state = max(current_fuzzy.items(), key=lambda x: x[1])[0]

            if current_state in self.rules:
                # 根据规则概率选择下一状态
                next_states = self.rules[current_state]
                next_state = max(next_states.items(), key=lambda x: x[1])[0]

                # 去模糊化得到预测值
                next_fuzzy = {next_state: 1.0}
                predicted_value = self._defuzzify(next_fuzzy)
                predictions.append(predicted_value)
                last_value = predicted_value
            else:
                predictions.append(last_value)

        return predictions

class MultiSourceInformationFusion:
    """多源信息融合模块"""
    def __init__(self):
        self.sentiment_analyzer = MarketSentimentAnalyzer()
        self.feature_extractor = AdvancedFeatureExtractor()
        self.fuzzy_integral = FuzzyIntegral()
        self.fusion_weights = {
            'technical': 0.4,
            'sentiment': 0.3,
            'fundamental': 0.2,
            'news': 0.1
        }

    def extract_market_sentiment(self, price_data, volume_data, news_data=None):
        """提取市场情绪特征"""
        return self.sentiment_analyzer.analyze(price_data, volume_data, news_data)

    def extract_technical_features(self, price_data, volume_data):
        """提取技术特征"""
        return self.feature_extractor.extract_technical_features(price_data, volume_data)

    def fuse_information(self, technical_signals, sentiment_signals, fundamental_signals=None, news_signals=None):
        """使用模糊积分融合多源信息"""
        sources = {
            'technical': technical_signals,
            'sentiment': sentiment_signals,
            'fundamental': fundamental_signals or [0.5] * len(technical_signals),
            'news': news_signals or [0.5] * len(technical_signals)
        }

        fused_signals = []
        for i in range(len(technical_signals)):
            signal_values = [sources[key][i] for key in sources.keys()]
            weights = list(self.fusion_weights.values())

            # 使用Choquet积分进行融合
            fused_value = self.fuzzy_integral.choquet_integral(signal_values, weights)
            fused_signals.append(fused_value)

        return fused_signals

class MarketSentimentAnalyzer:
    """市场情绪分析器"""
    def __init__(self):
        self.sentiment_indicators = {
            'fear_greed': 0.5,
            'volatility_sentiment': 0.5,
            'volume_sentiment': 0.5,
            'price_momentum': 0.5
        }

    def analyze(self, price_data, volume_data, news_data=None):
        """分析市场情绪"""
        if len(price_data) < 20:
            return [0.5] * len(price_data)

        sentiment_scores = []

        for i in range(len(price_data)):
            if i < 19:
                sentiment_scores.append(0.5)
                continue

            # 恐惧贪婪指数
            fear_greed = self._calculate_fear_greed_index(price_data[i-19:i+1])

            # 波动率情绪
            volatility_sentiment = self._calculate_volatility_sentiment(price_data[i-19:i+1])

            # 成交量情绪
            volume_sentiment = self._calculate_volume_sentiment(volume_data[i-19:i+1] if i < len(volume_data) else [1000]*20)

            # 价格动量情绪
            momentum_sentiment = self._calculate_momentum_sentiment(price_data[i-19:i+1])

            # 综合情绪得分
            overall_sentiment = (
                fear_greed * 0.3 +
                volatility_sentiment * 0.25 +
                volume_sentiment * 0.25 +
                momentum_sentiment * 0.2
            )

            sentiment_scores.append(overall_sentiment)

        return sentiment_scores

    def _calculate_fear_greed_index(self, prices):
        """计算恐惧贪婪指数"""
        if len(prices) < 2:
            return 0.5

        # 基于价格变化的恐惧贪婪指数
        returns = np.diff(prices) / prices[:-1]
        avg_return = np.mean(returns)
        volatility = np.std(returns)

        # 标准化到0-1范围
        fear_greed = 0.5 + avg_return / (volatility + 1e-6) * 0.1
        return np.clip(fear_greed, 0, 1)

    def _calculate_volatility_sentiment(self, prices):
        """计算波动率情绪"""
        if len(prices) < 2:
            return 0.5

        returns = np.diff(prices) / prices[:-1]
        volatility = np.std(returns)

        # 高波动率表示恐慌，低波动率表示平静
        sentiment = 1 / (1 + volatility * 100)  # 反向关系
        return sentiment

    def _calculate_volume_sentiment(self, volumes):
        """计算成交量情绪"""
        if len(volumes) < 10:
            return 0.5

        recent_avg = np.mean(volumes[-5:])
        historical_avg = np.mean(volumes[:-5])

        # 成交量放大表示情绪激烈
        volume_ratio = recent_avg / (historical_avg + 1e-6)
        sentiment = 1 / (1 + np.exp(-(volume_ratio - 1)))  # Sigmoid函数
        return sentiment

    def _calculate_momentum_sentiment(self, prices):
        """计算价格动量情绪"""
        if len(prices) < 10:
            return 0.5

        short_ma = np.mean(prices[-5:])
        long_ma = np.mean(prices[-10:])

        momentum = (short_ma - long_ma) / long_ma
        sentiment = 0.5 + momentum * 0.5  # 转换到0-1范围
        return np.clip(sentiment, 0, 1)

class AdvancedFeatureExtractor:
    """高级特征提取器"""
    def __init__(self):
        self.feature_cache = {}

    def extract_technical_features(self, price_data, volume_data):
        """提取技术分析特征"""
        if len(price_data) < 20:
            return [0.5] * len(price_data)

        features = []

        for i in range(len(price_data)):
            if i < 19:
                features.append(0.5)
                continue

            window_prices = price_data[i-19:i+1]
            window_volumes = volume_data[i-19:i+1] if i < len(volume_data) else [1000]*20

            # 趋势特征
            trend_features = self._extract_trend_features(window_prices)

            # 动量特征
            momentum_features = self._extract_momentum_features(window_prices)

            # 波动率特征
            volatility_features = self._extract_volatility_features(window_prices)

            # 成交量特征
            volume_features = self._extract_volume_features(window_volumes)

            # 综合特征得分
            combined_score = (
                trend_features * 0.3 +
                momentum_features * 0.3 +
                volatility_features * 0.2 +
                volume_features * 0.2
            )

            features.append(combined_score)

        return features

    def _extract_trend_features(self, prices):
        """提取趋势特征"""
        if len(prices) < 10:
            return 0.5

        # 移动平均趋势
        short_ma = np.mean(prices[-5:])
        long_ma = np.mean(prices[-10:])

        trend_score = (short_ma - long_ma) / long_ma
        return 0.5 + trend_score * 0.5

    def _extract_momentum_features(self, prices):
        """提取动量特征"""
        if len(prices) < 5:
            return 0.5

        momentum = (prices[-1] - prices[-5]) / prices[-5]
        return 0.5 + momentum * 0.5

    def _extract_volatility_features(self, prices):
        """提取波动率特征"""
        if len(prices) < 2:
            return 0.5

        returns = np.diff(prices) / prices[:-1]
        volatility = np.std(returns)

        # 标准化波动率
        normalized_vol = 1 / (1 + volatility * 100)
        return normalized_vol

    def _extract_volume_features(self, volumes):
        """提取成交量特征"""
        if len(volumes) < 5:
            return 0.5

        recent_vol = np.mean(volumes[-3:])
        avg_vol = np.mean(volumes)

        volume_ratio = recent_vol / (avg_vol + 1e-6)
        return 1 / (1 + np.exp(-(volume_ratio - 1)))

class FuzzyIntegral:
    """模糊积分实现"""
    def __init__(self):
        self.measure_cache = {}

    def choquet_integral(self, values, weights):
        """Choquet积分"""
        if len(values) != len(weights):
            raise ValueError("值和权重的长度必须相同")

        # 按值排序
        sorted_indices = np.argsort(values)
        sorted_values = np.array(values)[sorted_indices]
        sorted_weights = np.array(weights)[sorted_indices]

        # 计算Choquet积分
        integral = 0
        cumulative_measure = 0

        for i in range(len(sorted_values)):
            if i == 0:
                measure_diff = sorted_weights[i]
            else:
                measure_diff = sorted_weights[i] - cumulative_measure

            integral += sorted_values[i] * measure_diff
            cumulative_measure += sorted_weights[i]

        return integral

    def sugeno_integral(self, values, weights):
        """Sugeno积分"""
        if len(values) != len(weights):
            raise ValueError("值和权重的长度必须相同")

        # 按值排序
        sorted_indices = np.argsort(values)[::-1]  # 降序
        sorted_values = np.array(values)[sorted_indices]
        sorted_weights = np.array(weights)[sorted_indices]

        # 计算Sugeno积分
        integral = 0.0
        for i in range(len(sorted_values)):
            measure = np.sum(sorted_weights[i:])  # 累积测度
            integral = max(integral, min(float(sorted_values[i]), float(measure)))

        return integral

class AdvancedDefuzzification:
    """改进的去模糊化方法"""

    @staticmethod
    def centroid_method(fuzzy_set, universe):
        """重心法去模糊化"""
        if len(fuzzy_set) != len(universe):
            raise ValueError("模糊集和论域长度必须相同")

        numerator = np.sum(np.array(fuzzy_set) * np.array(universe))
        denominator = np.sum(fuzzy_set)

        return numerator / denominator if denominator != 0 else 0

    @staticmethod
    def area_center_method(fuzzy_set, universe):
        """面积中心法去模糊化"""
        if len(fuzzy_set) != len(universe):
            raise ValueError("模糊集和论域长度必须相同")

        # 计算面积
        areas = []
        centers = []

        for i in range(len(fuzzy_set) - 1):
            if fuzzy_set[i] > 0 or fuzzy_set[i + 1] > 0:
                # 梯形面积
                area = (fuzzy_set[i] + fuzzy_set[i + 1]) * (universe[i + 1] - universe[i]) / 2
                center = (universe[i] + universe[i + 1]) / 2
                areas.append(area)
                centers.append(center)

        if not areas:
            return 0

        total_area = sum(areas)
        weighted_center = sum(area * center for area, center in zip(areas, centers))

        return weighted_center / total_area if total_area != 0 else 0

    @staticmethod
    def maximum_method(fuzzy_set, universe):
        """最大值法去模糊化"""
        max_index = np.argmax(fuzzy_set)
        return universe[max_index]

    @staticmethod
    def mean_of_maximum(fuzzy_set, universe):
        """最大值平均法去模糊化"""
        max_value = np.max(fuzzy_set)
        max_indices = np.where(np.array(fuzzy_set) == max_value)[0]

        if len(max_indices) == 0:
            return 0

        return np.mean([universe[i] for i in max_indices])

    @staticmethod
    def q_rofs_score_function(mu, nu, q=2):
        """q-ROFS得分函数"""
        pi = 1 - (mu**q + nu**q)**(1/q)  # 犹豫度
        score = (mu**q - nu**q + pi**q) / 2
        return score

    @staticmethod
    def q_rofs_accuracy_function(mu, nu, q=2):
        """q-ROFS精确度函数"""
        return mu**q + nu**q

class DynamicRuleLibrary:
    """动态规则库更新机制"""
    def __init__(self, max_rules=50, learning_rate=0.01):
        self.max_rules = max_rules
        self.learning_rate = learning_rate
        self.momentum = 0.9  # 动量系数
        self.rules = []
        self.rule_performance = {}
        self.rule_usage_count = {}
        self.adaptation_history = []

        # 机器学习相关属性
        self.model_weights = np.random.normal(0, 0.1, 20)
        self.velocity = np.zeros(20)  # 动量项
        self.feature_history = []
        self.price_history = []

    def add_rule(self, condition, action, initial_weight=1.0):
        """添加新规则"""
        rule_id = len(self.rules)
        rule = {
            'id': rule_id,
            'condition': condition,
            'action': action,
            'weight': initial_weight,
            'creation_time': datetime.now(),
            'last_used': None,
            'success_count': 0,
            'total_usage': 0
        }

        self.rules.append(rule)
        self.rule_performance[rule_id] = []
        self.rule_usage_count[rule_id] = 0

        # 如果规则数量超过限制，删除表现最差的规则
        if len(self.rules) > self.max_rules:
            self._prune_worst_rule()

        return rule_id

    def update_rule_performance(self, rule_id, performance_score):
        """更新规则性能"""
        if rule_id in self.rule_performance:
            self.rule_performance[rule_id].append(performance_score)

            # 保持性能历史在合理范围内
            if len(self.rule_performance[rule_id]) > 100:
                self.rule_performance[rule_id].pop(0)

            # 更新规则权重
            avg_performance = np.mean(self.rule_performance[rule_id])
            # 确保rule_id是有效的索引
            if 0 <= rule_id < len(self.rules):
                self.rules[rule_id]['weight'] *= (1 + self.learning_rate * (avg_performance - 0.5))
                self.rules[rule_id]['weight'] = np.clip(self.rules[rule_id]['weight'], 0.1, 2.0)

    def incremental_learning(self, new_data, labels):
        """增量学习新规则"""
        # 使用模糊聚类提取新规则
        clusterer = FuzzyCMeansClusterer(n_clusters=min(5, len(new_data) // 10))

        try:
            new_rules = clusterer.extract_fuzzy_rules(new_data, labels)

            for rule_info in new_rules:
                if rule_info['confidence'] > 0.7:  # 只添加高置信度规则
                    condition = self._create_condition_from_cluster(rule_info)
                    action = self._create_action_from_output(rule_info['output'])
                    self.add_rule(condition, action, rule_info['confidence'])

        except Exception as e:
            print(f"增量学习失败: {e}")

    def automatic_rule_generation(self, historical_data, performance_feedback):
        """高级自动规则生成机制"""
        try:
            # 1. 基于遗传算法的规则进化
            evolved_rules = self._genetic_algorithm_rule_evolution(historical_data, performance_feedback)

            # 2. 基于强化学习的规则优化
            optimized_rules = self._reinforcement_learning_rule_optimization(historical_data, performance_feedback)

            # 3. 基于模式识别的规则发现
            pattern_rules = self._pattern_based_rule_discovery(historical_data)

            # 4. 基于多目标优化的规则生成
            multi_objective_rules = self._multi_objective_rule_generation(historical_data, performance_feedback)

            # 合并所有生成的规则
            all_new_rules = evolved_rules + optimized_rules + pattern_rules + multi_objective_rules

            # 评估和筛选规则
            validated_rules = self._validate_and_rank_rules(all_new_rules, historical_data)

            # 添加最佳规则到规则库
            for rule in validated_rules[:5]:  # 只添加前5个最佳规则
                self.add_rule(rule['condition'], rule['action'], rule['confidence'])

            return len(validated_rules)

        except Exception as e:
            print(f"自动规则生成失败: {e}")
            return 0

    def _genetic_algorithm_rule_evolution(self, historical_data, performance_feedback):
        """基于遗传算法的规则进化"""
        population_size = 20
        generations = 10
        mutation_rate = 0.1

        # 初始化种群
        population = self._initialize_rule_population(population_size)

        for generation in range(generations):
            # 评估适应度
            fitness_scores = []
            for individual in population:
                fitness = self._evaluate_rule_fitness(individual, historical_data, performance_feedback)
                fitness_scores.append(fitness)

            # 选择
            selected = self._selection(population, fitness_scores)

            # 交叉
            offspring = self._crossover(selected)

            # 变异
            mutated = self._mutation(offspring, mutation_rate)

            population = mutated

        # 返回最佳规则
        final_fitness = [self._evaluate_rule_fitness(ind, historical_data, performance_feedback) for ind in population]
        best_indices = np.argsort(final_fitness)[-5:]  # 选择前5个

        return [population[i] for i in best_indices]

    def _reinforcement_learning_rule_optimization(self, historical_data, performance_feedback):
        """基于强化学习的规则优化"""
        # Q-learning参数
        learning_rate = 0.1
        discount_factor = 0.9
        epsilon = 0.1
        episodes = 100

        # 状态空间：市场条件的离散化
        state_space = self._discretize_market_conditions(historical_data)

        # 动作空间：可能的规则参数组合
        action_space = self._generate_rule_action_space()

        # Q表初始化
        q_table = np.zeros((len(state_space), len(action_space)))

        # Q-learning训练
        for episode in range(episodes):
            state_idx = np.random.randint(0, len(state_space))

            for step in range(min(50, len(historical_data))):
                # ε-贪婪策略选择动作
                if np.random.random() < epsilon:
                    action_idx = np.random.randint(0, len(action_space))
                else:
                    action_idx = np.argmax(q_table[state_idx])

                # 执行动作并获得奖励
                reward = self._calculate_rule_reward(
                    state_space[state_idx],
                    action_space[action_idx],
                    performance_feedback
                )

                # 更新Q值
                next_state_idx = (state_idx + 1) % len(state_space)
                q_table[state_idx, action_idx] += learning_rate * (
                    reward + discount_factor * np.max(q_table[next_state_idx]) - q_table[state_idx, action_idx]
                )

                state_idx = next_state_idx

        # 提取最佳规则
        best_rules = []
        for state_idx in range(len(state_space)):
            best_action_idx = np.argmax(q_table[state_idx])
            rule = self._create_rule_from_state_action(state_space[state_idx], action_space[best_action_idx])
            if rule:
                best_rules.append(rule)

        return best_rules[:3]  # 返回前3个最佳规则

    def _pattern_based_rule_discovery(self, historical_data):
        """基于模式识别的规则发现"""
        discovered_rules = []

        # 1. 时间序列模式识别
        time_patterns = self._discover_temporal_patterns(historical_data)

        # 2. 频繁项集挖掘
        frequent_patterns = self._mine_frequent_patterns(historical_data)

        # 3. 关联规则挖掘
        association_rules = self._mine_association_rules(historical_data)

        # 4. 异常模式检测
        anomaly_patterns = self._detect_anomaly_patterns(historical_data)

        # 将模式转换为模糊规则
        for pattern in time_patterns + frequent_patterns + association_rules + anomaly_patterns:
            rule = self._pattern_to_fuzzy_rule(pattern)
            if rule:
                discovered_rules.append(rule)

        return discovered_rules

    def _multi_objective_rule_generation(self, historical_data, performance_feedback):
        """基于多目标优化的规则生成"""
        # 目标函数：准确率、收益率、风险控制
        objectives = ['accuracy', 'profitability', 'risk_control']

        # NSGA-II算法参数
        population_size = 30
        generations = 15

        # 初始化种群
        population = self._initialize_rule_population(population_size)

        for generation in range(generations):
            # 多目标评估
            objective_scores = []
            for individual in population:
                scores = self._evaluate_multi_objectives(individual, historical_data, performance_feedback)
                objective_scores.append(scores)

            # 非支配排序
            fronts = self._non_dominated_sorting(objective_scores)

            # 拥挤距离计算
            crowding_distances = self._calculate_crowding_distance(objective_scores, fronts)

            # 选择下一代
            population = self._nsga_selection(population, fronts, crowding_distances)

            # 交叉和变异
            offspring = self._crossover(population)
            population = self._mutation(offspring, 0.1)

        # 返回帕累托前沿的规则
        final_scores = [self._evaluate_multi_objectives(ind, historical_data, performance_feedback) for ind in population]
        pareto_front = self._get_pareto_front(population, final_scores)

        return pareto_front

    def _initialize_rule_population(self, size):
        """初始化规则种群"""
        population = []
        for _ in range(size):
            rule = {
                'condition_params': np.random.uniform(0, 1, 6),  # 3个变量，每个2个参数
                'action_params': np.random.uniform(0, 1, 2),    # 动作参数
                'confidence': np.random.uniform(0.5, 1.0)
            }
            population.append(rule)
        return population

    def _evaluate_rule_fitness(self, rule, historical_data, performance_feedback):
        """评估规则适应度"""
        # 简化的适应度函数
        accuracy = self._calculate_rule_accuracy(rule, historical_data)
        profitability = self._calculate_rule_profitability(rule, performance_feedback)
        stability = self._calculate_rule_stability(rule, historical_data)

        # 加权组合
        fitness = 0.4 * accuracy + 0.4 * profitability + 0.2 * stability
        return fitness

    def _calculate_rule_accuracy(self, rule, historical_data):
        """计算规则准确率"""
        if not historical_data:
            return 0.5

        correct_predictions = 0
        total_predictions = 0

        for data_point in historical_data[-50:]:  # 使用最近50个数据点
            predicted_action = self._apply_rule_to_data(rule, data_point)
            actual_outcome = data_point.get('actual_outcome', 'hold')

            if predicted_action == actual_outcome:
                correct_predictions += 1
            total_predictions += 1

        return correct_predictions / total_predictions if total_predictions > 0 else 0.5

    def _calculate_rule_profitability(self, rule, performance_feedback):
        """计算规则盈利能力"""
        if not performance_feedback:
            return 0.5

        # 基于历史性能反馈计算盈利能力
        profits = [fb.get('profit', 0) for fb in performance_feedback[-20:]]
        return np.mean(profits) if profits else 0.5

    def _calculate_rule_stability(self, rule, historical_data):
        """计算规则稳定性"""
        if len(historical_data) < 10:
            return 0.5

        # 计算规则在不同市场条件下的表现一致性
        performances = []
        for i in range(0, len(historical_data) - 10, 10):
            subset = historical_data[i:i+10]
            perf = self._evaluate_rule_on_subset(rule, subset)
            performances.append(perf)

        # 稳定性 = 1 - 变异系数
        if len(performances) > 1:
            cv = np.std(performances) / (np.mean(performances) + 1e-6)
            stability = max(0, 1 - cv)
        else:
            stability = 0.5

        return stability

    def _discretize_market_conditions(self, historical_data):
        """离散化市场条件"""
        # 简化的市场状态离散化
        states = []
        for data in historical_data:
            stability = data.get('stability', 0.5)
            volatility = data.get('volatility', 0.5)
            profit = data.get('profit', 0.0)

            # 将连续值离散化为状态
            state = (
                int(stability * 3),  # 0, 1, 2
                int(volatility * 3),  # 0, 1, 2
                int((profit + 1) * 1.5)  # -1到1映射到0, 1, 2
            )
            states.append(state)

        return list(set(states))  # 去重

    def _generate_rule_action_space(self):
        """生成规则动作空间"""
        actions = []
        # 生成不同的规则参数组合
        for risk_threshold in [0.3, 0.5, 0.7]:
            for action_strength in [0.2, 0.5, 0.8]:
                actions.append({
                    'risk_threshold': risk_threshold,
                    'action_strength': action_strength
                })
        return actions

    def _calculate_rule_reward(self, state, action, performance_feedback):
        """计算规则奖励"""
        # 基于状态-动作对计算奖励
        base_reward = 0.5

        # 根据性能反馈调整奖励
        if performance_feedback:
            recent_performance = np.mean([fb.get('profit', 0) for fb in performance_feedback[-5:]])
            base_reward += recent_performance * 0.5

        # 根据动作强度调整
        action_bonus = action.get('action_strength', 0.5) * 0.2

        return np.clip(base_reward + action_bonus, 0, 1)

    def _create_rule_from_state_action(self, state, action):
        """从状态-动作对创建规则"""
        try:
            stability_level, volatility_level, profit_level = state

            # 创建条件函数
            def condition(**inputs):
                s_mem = inputs.get('stability', {})
                v_mem = inputs.get('volatility', {})
                p_mem = inputs.get('profit', {})

                # 简化的条件评估
                s_score = s_mem.get('high', 0) if stability_level == 2 else s_mem.get('medium', 0)
                v_score = v_mem.get('high', 0) if volatility_level == 2 else v_mem.get('low', 0)
                p_score = p_mem.get('positive', 0) if profit_level == 2 else p_mem.get('negative', 0)

                return min(s_score, v_score, p_score) * action.get('action_strength', 0.5)

            # 创建动作函数
            def action_func():
                risk_level = 'RiskLow' if action.get('risk_threshold', 0.5) < 0.4 else 'RiskMedium'
                action_level = 'Aggressive' if action.get('action_strength', 0.5) > 0.6 else 'Normal'
                return (risk_level, action_level)

            return {
                'condition': condition,
                'action': action_func,
                'confidence': 0.7
            }
        except Exception:
            return None

    def _discover_temporal_patterns(self, historical_data):
        """发现时间序列模式"""
        patterns = []

        if len(historical_data) < 10:
            return patterns

        # 寻找周期性模式
        for window_size in [3, 5, 7]:
            for i in range(len(historical_data) - window_size):
                window = historical_data[i:i+window_size]
                pattern = self._extract_pattern_features(window)
                if pattern:
                    patterns.append(pattern)

        return patterns[:5]  # 返回前5个模式

    def _mine_frequent_patterns(self, historical_data):
        """挖掘频繁模式"""
        patterns = []

        # 简化的频繁项集挖掘
        item_counts = {}
        for data in historical_data:
            items = self._data_to_items(data)
            for item in items:
                item_counts[item] = item_counts.get(item, 0) + 1

        # 选择频繁项
        min_support = len(historical_data) * 0.3
        frequent_items = [item for item, count in item_counts.items() if count >= min_support]

        for item in frequent_items[:3]:
            patterns.append({'type': 'frequent', 'item': item})

        return patterns

    def _mine_association_rules(self, historical_data):
        """挖掘关联规则"""
        rules = []

        # 简化的关联规则挖掘
        for i in range(len(historical_data) - 1):
            current = historical_data[i]
            next_data = historical_data[i + 1]

            # 寻找条件->结果的关联
            condition = self._extract_condition_features(current)
            result = self._extract_result_features(next_data)

            if condition and result:
                rules.append({
                    'type': 'association',
                    'condition': condition,
                    'result': result
                })

        return rules[:3]

    def _detect_anomaly_patterns(self, historical_data):
        """检测异常模式"""
        anomalies = []

        if len(historical_data) < 20:
            return anomalies

        # 使用简单的统计方法检测异常
        features = []
        for data in historical_data:
            feature = [
                data.get('stability', 0.5),
                data.get('volatility', 0.5),
                data.get('profit', 0.0)
            ]
            features.append(feature)

        features = np.array(features)

        # 计算Z-score
        mean = np.mean(features, axis=0)
        std = np.std(features, axis=0)

        for i, feature in enumerate(features):
            z_scores = np.abs((feature - mean) / (std + 1e-6))
            if np.any(z_scores > 2.5):  # 异常阈值
                anomalies.append({
                    'type': 'anomaly',
                    'index': i,
                    'z_scores': z_scores.tolist()
                })

        return anomalies[:2]

    def _pattern_to_fuzzy_rule(self, pattern):
        """将模式转换为模糊规则"""
        try:
            pattern_type = pattern.get('type', 'unknown')

            if pattern_type == 'frequent':
                return self._frequent_pattern_to_rule(pattern)
            elif pattern_type == 'association':
                return self._association_pattern_to_rule(pattern)
            elif pattern_type == 'anomaly':
                return self._anomaly_pattern_to_rule(pattern)
            else:
                return None
        except Exception:
            return None

    def _frequent_pattern_to_rule(self, pattern):
        """频繁模式转规则"""
        item = pattern.get('item', '')

        def condition(**inputs):
            # 基于频繁项创建条件
            return 0.7 if item in str(inputs) else 0.0

        def action():
            return ('RiskMedium', 'Normal')

        return {
            'condition': condition,
            'action': action,
            'confidence': 0.6
        }

    def _association_pattern_to_rule(self, pattern):
        """关联模式转规则"""
        condition_features = pattern.get('condition', {})
        result_features = pattern.get('result', {})

        def condition(**inputs):
            # 基于关联规则创建条件
            score = 0.5
            for key, value in condition_features.items():
                if key in inputs:
                    score += 0.2
            return min(score, 1.0)

        def action():
            # 基于结果特征确定动作
            risk = 'RiskLow' if result_features.get('risk', 0.5) < 0.5 else 'RiskHigh'
            action_level = 'Conservative' if result_features.get('action', 0.5) < 0.5 else 'Aggressive'
            return (risk, action_level)

        return {
            'condition': condition,
            'action': action,
            'confidence': 0.65
        }

    def reinforcement_learning_update(self, rule_id, reward):
        """强化学习规则更新"""
        if rule_id < len(self.rules):
            # Q-learning风格的更新
            current_weight = self.rules[rule_id]['weight']
            new_weight = current_weight + self.learning_rate * (reward - current_weight)
            self.rules[rule_id]['weight'] = np.clip(new_weight, 0.1, 2.0)

            # 记录适应历史
            self.adaptation_history.append({
                'rule_id': rule_id,
                'old_weight': current_weight,
                'new_weight': new_weight,
                'reward': reward,
                'timestamp': datetime.now()
            })

    def _prune_worst_rule(self):
        """删除表现最差的规则"""
        if len(self.rules) <= 5:  # 保持最少5个规则
            return

        worst_rule_id = None
        worst_score = float('inf')

        for rule in self.rules:
            rule_id = rule['id']
            if rule_id in self.rule_performance and self.rule_performance[rule_id]:
                avg_performance = np.mean(self.rule_performance[rule_id])
                usage_penalty = 1 / (self.rule_usage_count.get(rule_id, 1) + 1)
                score = avg_performance - usage_penalty

                if score < worst_score:
                    worst_score = score
                    worst_rule_id = rule_id

        if worst_rule_id is not None:
            self._remove_rule(worst_rule_id)

    def _remove_rule(self, rule_id):
        """删除指定规则"""
        self.rules = [rule for rule in self.rules if rule['id'] != rule_id]
        if rule_id in self.rule_performance:
            del self.rule_performance[rule_id]
        if rule_id in self.rule_usage_count:
            del self.rule_usage_count[rule_id]

    def _create_condition_from_cluster(self, rule_info):
        """从聚类信息创建条件函数（增强异常处理）"""
        try:
            # 验证输入数据
            if not isinstance(rule_info, dict):
                raise ValueError("rule_info必须是字典类型")

            if 'features' not in rule_info:
                raise ValueError("rule_info缺少features字段")

            features = rule_info['features']
            if 'center' not in features or 'std' not in features:
                raise ValueError("features缺少center或std字段")

            center = np.array(features['center'])
            std = np.array(features['std'])

            # 验证数据有效性
            if len(center) == 0 or len(std) == 0:
                raise ValueError("center或std为空")

            if np.any(np.isnan(center)) or np.any(np.isnan(std)):
                raise ValueError("center或std包含NaN值")

            # 计算阈值，避免除零错误
            threshold_base = np.mean(std) if len(std) > 0 else 1.0
            threshold_multiplier = 2.0

            def condition(s_mem, v_mem, p_mem):
                try:
                    # 验证输入参数
                    if not all(isinstance(mem, dict) for mem in [s_mem, v_mem, p_mem]):
                        return False

                    # 构建输入向量，使用默认值处理缺失键
                    input_vector = np.array([
                        s_mem.get('Low', 0.0), s_mem.get('Medium', 0.0), s_mem.get('High', 0.0),
                        v_mem.get('Low', 0.0), v_mem.get('Medium', 0.0), v_mem.get('High', 0.0),
                        p_mem.get('Negative', 0.0), p_mem.get('Low', 0.0),
                        p_mem.get('Medium', 0.0), p_mem.get('High', 0.0)
                    ])

                    # 验证输入向量
                    if np.any(np.isnan(input_vector)) or np.any(np.isinf(input_vector)):
                        return False

                    # 计算与聚类中心的距离
                    vector_len = min(len(input_vector), len(center))
                    if vector_len == 0:
                        return False

                    distance = np.linalg.norm(input_vector[:vector_len] - center[:vector_len])
                    threshold = threshold_base * threshold_multiplier

                    # 避免数值问题
                    if np.isnan(distance) or np.isinf(distance):
                        return False

                    return distance < threshold

                except Exception as e:
                    # 条件函数内部异常，返回False而不是抛出异常
                    print(f"条件函数执行异常: {e}")
                    return False

            return condition

        except Exception as e:
            print(f"创建条件函数失败: {e}")
            # 返回一个总是返回False的安全条件函数
            def safe_condition(s_mem, v_mem, p_mem):
                return False
            return safe_condition

    def _create_action_from_output(self, output_class):
        """从输出类别创建动作函数"""
        actions = [
            ("RiskLow", "Conservative"),
            ("RiskMedium", "Normal"),
            ("RiskHigh", "Aggressive")
        ]

        action_index = min(output_class, len(actions) - 1)
        selected_action = actions[action_index]

        def action():
            return selected_action + (0.8,)  # 添加置信度

        return action

    def get_active_rules(self, min_weight=0.3):
        """获取活跃规则"""
        return [rule for rule in self.rules if rule['weight'] >= min_weight]

    def get_rule_statistics(self):
        """获取规则库统计信息"""
        total_rules = len(self.rules)
        avg_weight = np.mean([rule['weight'] for rule in self.rules]) if self.rules else 0
        active_rules = len(self.get_active_rules())

        return {
            'total_rules': total_rules,
            'active_rules': active_rules,
            'average_weight': avg_weight,
            'adaptation_count': len(self.adaptation_history)
        }

    def extract_features(self, prices: List[float], volumes: List[float] = None) -> np.ndarray:
        """提取技术特征"""
        if len(prices) < 20:
            return np.zeros(20)

        price_array = np.array(prices[-20:], dtype=np.float64)
        features = []

        # 价格特征
        features.extend([
            float(np.mean(price_array)),  # 均价
            float(np.std(price_array)),   # 波动率
            float((price_array[-1] - price_array[0]) / price_array[0]),  # 收益率
            float(np.max(price_array) / np.min(price_array) - 1),   # 价格范围
        ])

        # 技术指标特征
        sma_5 = np.mean(prices[-5:])
        sma_10 = np.mean(prices[-10:])
        sma_20 = np.mean(prices[-20:])

        features.extend([
            (prices[-1] - sma_5) / sma_5,    # 相对SMA5
            (prices[-1] - sma_10) / sma_10,  # 相对SMA10
            (prices[-1] - sma_20) / sma_20,  # 相对SMA20
            (sma_5 - sma_10) / sma_10,       # SMA5-SMA10差值
        ])

        # 动量特征
        returns = np.diff(prices) / prices[:-1]
        # 确保returns是数值类型
        returns = np.array([float(r) for r in returns if not np.isnan(r) and not np.isinf(r)])

        if len(returns) > 0:
            features.extend([
                float(np.mean(returns)),     # 平均收益率
                float(np.std(returns)),      # 收益率波动
                float(np.sum(returns > 0) / len(returns)),  # 上涨概率
                float(np.max(returns)),      # 最大收益率
                float(np.min(returns)),      # 最小收益率
            ])
        else:
            features.extend([0.0, 0.0, 0.5, 0.0, 0.0])

        # RSI特征
        gains = np.where(returns > 0, returns, 0)
        losses = np.where(returns < 0, -returns, 0)
        avg_gain = np.mean(gains) if len(gains) > 0 else 0
        avg_loss = np.mean(losses) if len(losses) > 0 else 0
        rsi = 100 - (100 / (1 + avg_gain / (avg_loss + 1e-10)))
        features.append(rsi / 100)

        # 布林带特征
        bb_upper = sma_20 + 2 * np.std(prices)
        bb_lower = sma_20 - 2 * np.std(prices)
        bb_position = (prices[-1] - bb_lower) / (bb_upper - bb_lower + 1e-10)
        features.append(bb_position)

        # MACD特征
        ema_12 = prices[-1]  # 简化
        ema_26 = sma_20
        macd = (ema_12 - ema_26) / ema_26
        features.append(macd)

        # 成交量特征（如果有的话）
        if volumes and len(volumes) >= 5:
            vol_features = [
                np.mean(volumes[-5:]) / (np.mean(volumes[-10:]) + 1e-10),  # 成交量比率
                np.std(volumes[-5:]) / (np.mean(volumes[-5:]) + 1e-10),    # 成交量波动
            ]
            features.extend(vol_features)
        else:
            features.extend([1.0, 0.1])  # 默认值

        # 时间特征
        hour = datetime.now().hour
        features.append(np.sin(2 * np.pi * hour / 24))  # 小时周期性

        return np.array(features[:20])  # 确保特征数量固定

    def predict_direction(self, features: np.ndarray) -> Tuple[float, float]:
        """预测价格方向和置信度"""
        # 简化的线性预测模型
        prediction = np.dot(features, self.model_weights)
        confidence = 1 / (1 + np.exp(-abs(prediction)))  # Sigmoid置信度
        direction = np.tanh(prediction)  # 方向预测 (-1到1)

        return direction, confidence

    def update_model(self, features: np.ndarray, actual_return: float):
        """在线学习更新模型"""
        prediction, _ = self.predict_direction(features)
        error = float(actual_return - prediction)

        # 使用动量梯度下降更新权重
        gradient = error * features.astype(np.float64)
        self.velocity = self.momentum * self.velocity + self.learning_rate * gradient
        self.model_weights += self.velocity

        # 权重衰减防止过拟合
        self.model_weights *= 0.9999

    def add_training_data(self, features: np.ndarray, price_change: float):
        """添加训练数据"""
        self.feature_history.append(features)
        self.price_history.append(price_change)

        # 保持历史数据在合理范围内
        if len(self.feature_history) > 1000:
            self.feature_history.pop(0)
            self.price_history.pop(0)

        # 每10个样本进行一次批量更新
        if len(self.feature_history) % 10 == 0:
            self.batch_update()

    def batch_update(self):
        """批量更新模型"""
        if len(self.feature_history) < 10:
            return

        # 使用最近的数据进行批量训练
        recent_features = np.array(self.feature_history[-50:])
        recent_returns = np.array(self.price_history[-50:])

        for features, actual_return in zip(recent_features, recent_returns):
            self.update_model(features, actual_return)

# 高级风险管理模块
class AdvancedRiskManager:
    """高级风险管理系统"""
    def __init__(self):
        self.var_history = []
        self.drawdown_history = []
        self.correlation_matrix = np.eye(3)  # 假设3个资产的相关性
        self.risk_budget = 0.02  # 2%的风险预算
        self.max_leverage = 3.0
        self.stress_scenarios = self._initialize_stress_scenarios()

    def _initialize_stress_scenarios(self) -> List[dict]:
        """初始化压力测试场景"""
        return [
            {"name": "市场崩盘", "price_shock": -0.20, "volatility_shock": 3.0},
            {"name": "流动性危机", "price_shock": -0.10, "volatility_shock": 2.0},
            {"name": "黑天鹅事件", "price_shock": -0.30, "volatility_shock": 5.0},
            {"name": "正常波动", "price_shock": 0.05, "volatility_shock": 1.2},
        ]

    def calculate_var(self, returns: List[float], confidence: float = 0.95) -> float:
        """计算风险价值(VaR)"""
        if len(returns) < 30:
            return 0.02  # 默认2%

        returns_array = np.array(returns, dtype=np.float64)
        var = float(np.percentile(returns_array, (1 - confidence) * 100))

        self.var_history.append(abs(var))
        if len(self.var_history) > 252:  # 保持一年的历史
            self.var_history.pop(0)

        return abs(var)

    def calculate_expected_shortfall(self, returns: List[float], confidence: float = 0.95) -> float:
        """计算期望损失(ES/CVaR)"""
        if len(returns) < 30:
            return 0.03

        returns_array = np.array(returns, dtype=np.float64)
        var_threshold = float(np.percentile(returns_array, (1 - confidence) * 100))
        tail_losses = returns_array[returns_array <= var_threshold]

        return float(abs(np.mean(tail_losses))) if len(tail_losses) > 0 else 0.03

    def stress_test(self, current_position: float, current_price: float) -> dict:
        """压力测试"""
        results = {}

        for scenario in self.stress_scenarios:
            shocked_price = current_price * (1 + scenario["price_shock"])
            pnl = current_position * (shocked_price - current_price)
            pnl_pct = pnl / (current_price * abs(current_position)) if current_position != 0 else 0

            results[scenario["name"]] = {
                "pnl": pnl,
                "pnl_pct": pnl_pct,
                "shocked_price": shocked_price
            }

        return results

    def calculate_optimal_position_size(self, expected_return: float, volatility: float,
                                      current_price: float, account_value: float) -> float:
        """使用Kelly公式计算最优仓位大小"""
        if volatility <= 0:
            return 0

        # Kelly公式: f = (bp - q) / b
        # 其中 b = 赔率, p = 胜率, q = 败率
        win_prob = norm.cdf(expected_return / volatility)  # 基于正态分布的胜率估计

        if win_prob <= 0.5:
            return 0

        # 简化的Kelly公式
        kelly_fraction = (expected_return / volatility**2) * 0.5  # 保守系数
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # 限制在25%以内

        # 考虑风险预算
        risk_adjusted_fraction = min(kelly_fraction, self.risk_budget)

        # 转换为实际仓位大小
        position_value = account_value * risk_adjusted_fraction
        position_size = position_value / current_price

        return position_size

# 模糊理论实现
class FuzzySystem:
    """基于模糊理论的决策系统"""
    def __init__(self):
        self.q = 2  # q-ROFS参数
        # 模糊集定义
        self.stability_sets = {
            "Low": lambda x: max(0, min(1, (0.4 - x) * 5)) if x < 0.4 else 0,
            "Medium": lambda x: max(0, min(1, (x - 0.3) * 5, (0.7 - x) * 5)),
            "High": lambda x: max(0, min(1, (x - 0.6) * 5))
        }

        self.volatility_sets = {
            "Low": lambda x: max(0, min(1, (0.03 - x) * 50)) if x < 0.03 else 0,
            "Medium": lambda x: max(0, min(1, (x - 0.02) * 50, (0.06 - x) * 50)),
            "High": lambda x: max(0, min(1, (x - 0.05) * 20))
        }

        self.profit_sets = {
            "Negative": lambda x: max(0, min(1, (0 - x) * 100)) if x < 0 else 0,
            "Low": lambda x: max(0, min(1, (x - 0.01) * 100, (0.03 - x) * 100)),
            "Medium": lambda x: max(0, min(1, (x - 0.02) * 100, (0.05 - x) * 100)),
            "High": lambda x: max(0, min(1, (x - 0.04) * 50))
        }
        
        # 模糊规则库
        self.rules = [
            # 低稳定性规则
            {"condition": lambda s, v, _p: s["Low"] > 0.7 and v["Low"] > 0.7,
             "action": lambda: ("RiskLow", "Aggressive")},
            {"condition": lambda s, v, _p: s["Low"] > 0.7 and v["Medium"] > 0.7,
             "action": lambda: ("RiskLow", "Conservative")},
            {"condition": lambda s, v, _p: s["Low"] > 0.7 and v["High"] > 0.7,
             "action": lambda: ("RiskNone", "Stop")},

            # 中等稳定性规则
            {"condition": lambda s, v, p: s["Medium"] > 0.7 and v["Low"] > 0.7 and p["Medium"] > 0.7,
             "action": lambda: ("RiskMedium", "Aggressive")},
            {"condition": lambda s, v, p: s["Medium"] > 0.7 and v["Low"] > 0.7 and p["Low"] > 0.7,
             "action": lambda: ("RiskMedium", "Normal")},
            {"condition": lambda s, v, _p: s["Medium"] > 0.7 and v["Medium"] > 0.7,
             "action": lambda: ("RiskLow", "Conservative")},
            {"condition": lambda s, v, _p: s["Medium"] > 0.7 and v["High"] > 0.7,
             "action": lambda: ("RiskNone", "Stop")},

            # 高稳定性规则
            {"condition": lambda s, v, p: s["High"] > 0.7 and v["Low"] > 0.7 and p["High"] > 0.7,
             "action": lambda: ("RiskHigh", "Aggressive")},
            {"condition": lambda s, v, p: s["High"] > 0.7 and v["Low"] > 0.7 and p["Medium"] > 0.7,
             "action": lambda: ("RiskHigh", "Normal")},
            {"condition": lambda s, v, p: s["High"] > 0.7 and v["Low"] > 0.7 and p["Low"] > 0.7,
             "action": lambda: ("RiskMedium", "Normal")},
            {"condition": lambda s, v, _p: s["High"] > 0.7 and v["Medium"] > 0.7,
             "action": lambda: ("RiskMedium", "Conservative")},
            {"condition": lambda s, v, _p: s["High"] > 0.7 and v["High"] > 0.7,
             "action": lambda: ("RiskLow", "Stop")},
        ]
        
        # 去模糊化参数
        self.risk_levels = {
            "RiskNone": 0.0,
            "RiskLow": 0.3,
            "RiskMedium": 0.6,
            "RiskHigh": 0.9
        }
        
        self.action_levels = {
            "Stop": 0.0,
            "Conservative": 0.3,
            "Normal": 0.6,
            "Aggressive": 0.9
        }

    def fuzzify(self, stability, volatility, profit):
        s_membership = {k: f(stability) for k, f in self.stability_sets.items()}
        v_membership = {k: f(volatility) for k, f in self.volatility_sets.items()}
        p_membership = {k: f(profit) for k, f in self.profit_sets.items()}
        return s_membership, v_membership, p_membership

    def infer(self, s_mem, v_mem, p_mem):
        """执行模糊推理 - 优化版本"""
        activated_rules = []
        rule_strengths = []

        # 计算每个规则的激活强度
        for i, rule in enumerate(self.rules):
            strength = rule["condition"](s_mem, v_mem, p_mem)
            if strength > 0.1:  # 提高激活阈值
                action = rule["action"]()
                # 动态权重计算
                rule_weight = self._calculate_rule_weight(strength, i, s_mem, v_mem, p_mem)
                activated_rules.append((action, rule_weight))
                rule_strengths.append(strength)

        if not activated_rules:
            return ("RiskMedium", "Normal"), 0.3  # 降低默认置信度

        # 改进的加权平均计算
        risk_sum = 0.0
        action_sum = 0.0
        total_weight = sum(weight for _, weight in activated_rules)

        for (risk, action), weight in activated_rules:
            risk_sum += self.risk_levels[risk] * weight
            action_sum += self.action_levels[action] * weight

        avg_risk = risk_sum / total_weight
        avg_action = action_sum / total_weight

        # 动态置信度计算
        confidence = self._calculate_dynamic_confidence(rule_strengths, activated_rules)

        return (avg_risk, avg_action), confidence

    def _calculate_rule_weight(self, strength, rule_index, s_mem, v_mem, p_mem):
        """计算规则权重"""
        base_weight = strength

        # 规则重要性调整
        important_rules = [0, 1, 2, 5, 8]  # 重要规则索引
        if rule_index in important_rules:
            base_weight *= 1.3

        # 基于输入一致性的权重调整
        consistency_factor = self._calculate_input_consistency(s_mem, v_mem, p_mem)
        adjusted_weight = base_weight * consistency_factor

        return max(adjusted_weight, 0.1)

    def _calculate_input_consistency(self, s_mem, v_mem, p_mem):
        """计算输入一致性因子"""
        # 检查输入之间的一致性
        consistency = 1.0

        # 稳定性和波动率的一致性检查
        high_stability = s_mem.get("High", 0)
        high_volatility = v_mem.get("High", 0)

        if high_stability > 0.5 and high_volatility > 0.5:
            consistency *= 0.7  # 高稳定性和高波动性矛盾

        # 收益和风险的一致性检查
        high_profit = p_mem.get("High", 0)
        negative_profit = p_mem.get("Negative", 0)

        if high_profit > 0.5 and negative_profit > 0.5:
            consistency *= 0.6  # 高收益和负收益矛盾

        return max(consistency, 0.5)

    def _calculate_dynamic_confidence(self, rule_strengths, activated_rules):
        """计算动态置信度"""
        if not rule_strengths:
            return 0.3

        # 基于规则激活强度的置信度
        avg_strength = np.mean(rule_strengths)
        max_strength = max(rule_strengths)

        # 基于规则数量的置信度调整
        num_rules = len(activated_rules)
        if num_rules >= 3:
            rule_count_factor = 1.1  # 多规则激活提升置信度
        elif num_rules == 1:
            rule_count_factor = 0.8  # 单规则激活降低置信度
        else:
            rule_count_factor = 1.0

        # 基于规则一致性的置信度调整
        actions = [action[0] for action, _ in activated_rules]
        risk_levels = [action[1] for action, _ in activated_rules]

        action_consistency = len(set(actions)) / len(actions) if actions else 1.0
        risk_consistency = len(set(risk_levels)) / len(risk_levels) if risk_levels else 1.0

        consistency_factor = (2.0 - action_consistency - risk_consistency) / 2.0

        # 综合置信度计算
        base_confidence = (avg_strength + max_strength) / 2.0
        final_confidence = base_confidence * rule_count_factor * consistency_factor

        return min(max(final_confidence, 0.2), 0.9)

    def dynamic_weight(self, μ, _ν, π):
        """基于偏好度的动态权重"""
        return (μ**self.q + μ**self.q * π**self.q)**(1/self.q)

    def make_decision(self, stability, volatility, profit):
        """执行模糊决策 - 优化版本"""
        # 输入验证和预处理
        stability = max(0.0, min(1.0, stability))
        volatility = max(0.0, min(0.2, volatility))  # 限制波动率范围
        profit = max(-0.2, min(0.2, profit))  # 限制收益范围

        # 模糊化
        s_mem, v_mem, p_mem = self.fuzzify(stability, volatility, profit)

        # 增强的推理过程
        (risk_value, action_value), base_confidence = self.infer(s_mem, v_mem, p_mem)

        # 置信度调整 - 基于输入质量
        confidence_adjustment = self._calculate_confidence_adjustment(stability, volatility, profit)
        adjusted_confidence = base_confidence * confidence_adjustment

        # 映射回语义值 - 使用改进的映射方法
        risk_level = self._map_to_risk_level(risk_value, adjusted_confidence)
        action_level = self._map_to_action_level(action_value, adjusted_confidence)

        # 最终置信度计算
        final_confidence = min(max(adjusted_confidence, 0.1), 0.95)  # 限制在[0.1, 0.95]

        return (risk_level, action_level, final_confidence)

    def _calculate_confidence_adjustment(self, stability, volatility, profit):
        """计算置信度调整因子"""
        # 基于输入数据质量调整置信度
        quality_factor = 1.0

        # 稳定性因子
        if stability > 0.7:
            quality_factor *= 1.1  # 高稳定性提升置信度
        elif stability < 0.3:
            quality_factor *= 0.8  # 低稳定性降低置信度

        # 波动率因子
        if volatility > 0.05:  # 高波动
            quality_factor *= 0.7
        elif volatility < 0.01:  # 低波动
            quality_factor *= 1.05

        # 收益一致性因子
        if abs(profit) > 0.1:  # 极端收益
            quality_factor *= 0.8

        return min(max(quality_factor, 0.5), 1.5)

    def _map_to_risk_level(self, risk_value, confidence):
        """改进的风险等级映射"""
        # 基于置信度调整映射阈值
        if confidence < 0.5:
            # 低置信度时更保守
            if risk_value < 0.3:
                return "RiskHigh"  # 更保守的风险评估
            elif risk_value < 0.7:
                return "RiskMedium"
            else:
                return "RiskLow"
        else:
            # 正常映射
            return min(self.risk_levels.keys(),
                      key=lambda k: abs(float(self.risk_levels.get(k, 0.5)) - risk_value))

    def _map_to_action_level(self, action_value, confidence):
        """改进的行动等级映射"""
        # 基于置信度调整行动激进程度
        if confidence < 0.6:
            # 低置信度时更保守
            if action_value > 0.6:
                return "Normal"  # 降低激进程度
            elif action_value < 0.4:
                return "Conservative"
            else:
                return "Normal"
        else:
            # 正常映射
            return min(self.action_levels.keys(),
                      key=lambda k: abs(float(self.action_levels.get(k, 0.5)) - action_value))

class IntuitiveTrapezoidalFuzzyNumber:
    """
    直觉梯形模糊数（改进型）
    特点：
    - 支持非对称隶属函数
    - 集成犹豫度计算
    - 带重心坐标优化
    """
    def __init__(self, a, b, c, d, mu, nu, a1=None, d1=None):
        self.a = a  # 隶属函数左起点
        self.b = b  # 隶属函数左顶点
        self.c = c  # 隶属函数右顶点
        self.d = d  # 隶属函数右终点
        self.mu = mu  # 隶属度峰值
        self.nu = nu  # 非隶属度谷值
        self.a1 = a1 if a1 is not None else a  # 非隶属函数左起点
        self.d1 = d1 if d1 is not None else d  # 非隶属函数右终点

    def membership(self, x):
        """计算x的隶属度"""
        if x < self.a or x > self.d:
            return 0.0
        elif self.a <= x < self.b:
            return self.mu * (x - self.a) / (self.b - self.a)
        elif self.b <= x <= self.c:
            return self.mu
        else:  # self.c < x <= self.d
            return self.mu * (self.d - x) / (self.d - self.c)

    def non_membership(self, x):
        """计算x的非隶属度"""
        if x < self.a1 or x > self.d1:
            return 1.0
        elif self.a1 <= x < self.b:
            return self.nu + (1 - self.nu) * (x - self.a1) / (self.b - self.a1)
        elif self.b <= x <= self.c:
            return self.nu
        else:  # self.c < x <= self.d1
            return self.nu + (1 - self.nu) * (self.d1 - x) / (self.d1 - self.c)

    def hesitation(self, x):
        """计算x的犹豫度"""
        return 1 - self.membership(x) - self.non_membership(x)

    def centroid_x(self):
        """计算重心横坐标期望值"""
        # 隶属函数区域重心 (详细积分推导见:cite[8])
        x_l = (self.c**2 + self.d**2 - self.a**2 - self.b**2 +
               self.d*self.c - self.a*self.b) / (3*(self.c + self.d - self.b - self.a))
        
        # 非隶属函数区域重心
        x_f = (-self.nu*(self.a1**2 + self.b**2 + self.a1*self.b - self.c**2 - self.d1**2 - self.d1*self.c)
               - 2*self.a1**2 + self.b**2 + self.a1*self.b - self.c**2 + 2*self.d1**2 - self.c*self.d1
               ) / (3*self.nu*(self.c + self.d1 - self.a1 - self.b) + 3*(self.b + self.d1 - self.a1 - self.c))
        
        # 犹豫度区域重心 (简化计算)
        x_d = (self.a + self.b + self.c + self.d) / 4.0
        
        return (x_l + x_f + x_d) / 3.0

class TrapezoidalFuzzyNumber:
    """梯形模糊数实现"""
    def __init__(self, a, b, c, d):
        assert a <= b <= c <= d, "Invalid trapezoid parameters"
        self.a = a
        self.b = b
        self.c = c
        self.d = d
        
    def membership(self, x):
        """计算隶属度"""
        if x < self.a:
            return 0.0
        elif self.a <= x < self.b:
            return (x - self.a) / (self.b - self.a)
        elif self.b <= x <= self.c:
            return 1.0
        elif self.c < x <= self.d:
            return (self.d - x) / (self.d - self.c)
        else:
            return 0.0
    
    def __add__(self, other):
        """梯形模糊数加法"""
        return TrapezoidalFuzzyNumber(
            self.a + other.a,
            self.b + other.b,
            self.c + other.c,
            self.d + other.d
        )
    
    def __mul__(self, other):
        """梯形模糊数乘法"""
        return TrapezoidalFuzzyNumber(
            self.a * other.a,
            self.b * other.b,
            self.c * other.c,
            self.d * other.d
        )
    
    def distance(self, other):
        """计算梯形模糊数距离"""
        return math.sqrt(
            0.25 * ((self.a - other.a)**2 +
                   (self.b - other.b)**2 +
                   (self.c - other.c)**2 +
                   (self.d - other.d)**2)
        )
    
    def centroid(self):
        """计算重心去模糊化值"""
        return (self.a + self.b + self.c + self.d) / 4.0
    
    def __repr__(self):
        return f"TrapezoidalFuzzyNumber({self.a}, {self.b}, {self.c}, {self.d})"

class AdvancedFuzzySystem:
    """集成梯形模糊数的高级模糊系统"""
    def __init__(self):
        # 梯形模糊集定义
        self.stability_sets = {
            "Low": TrapezoidalFuzzyNumber(0.0, 0.1, 0.3, 0.4),
            "Medium": TrapezoidalFuzzyNumber(0.3, 0.4, 0.6, 0.7),
            "High": TrapezoidalFuzzyNumber(0.6, 0.7, 0.9, 1.0)
        }
        self.volatility_sets = {
            "Low": TrapezoidalFuzzyNumber(0.0, 0.01, 0.03, 0.05),
            "Medium": TrapezoidalFuzzyNumber(0.03, 0.05, 0.07, 0.09),
            "High": TrapezoidalFuzzyNumber(0.07, 0.09, 0.12, 0.15)
        }
        self.profit_sets = {
            "Negative": TrapezoidalFuzzyNumber(-0.1, -0.08, -0.03, 0.0),
            "Low": TrapezoidalFuzzyNumber(-0.02, 0.0, 0.02, 0.04),
            "Medium": TrapezoidalFuzzyNumber(0.02, 0.04, 0.06, 0.08),
            "High": TrapezoidalFuzzyNumber(0.06, 0.08, 0.12, 0.15)
        }

        # 集成新功能模块
        self.dynamic_rules = DynamicRuleLibrary(max_rules=30)
        self.defuzzifier = AdvancedDefuzzification()
        self.fuzzy_clustering = FuzzyCMeansClusterer(n_clusters=5)
        self.time_series_predictor = FuzzyTimeSeriesPredictor()
        self.info_fusion = MultiSourceInformationFusion()

        # 自适应参数
        self.adaptation_enabled = True
        self.learning_history = []
        self.performance_threshold = 0.6
        # 梯形模糊规则库
        self.rules = [
            {"condition": lambda s, v, _p: s.get("Low", 0) > 0.7 and v.get("Low", 0) > 0.7,
             "action": lambda: ("RiskLow", "Aggressive", 0.8)},
            {"condition": lambda s, v, p: s.get("Medium", 0) > 0.7 and v.get("Low", 0) > 0.7 and p.get("Medium", 0) > 0.7,
             "action": lambda: ("RiskMedium", "Aggressive", 0.9)},
            {"condition": lambda s, v, p: s.get("High", 0) > 0.7 and v.get("Low", 0) > 0.7 and p.get("High", 0) > 0.7,
             "action": lambda: ("RiskHigh", "Aggressive", 0.95)}
        ]
        # 风险-行动关联矩阵
        self.risk_action_matrix = {
            ("RiskLow", "Aggressive"): TrapezoidalFuzzyNumber(0.7, 0.75, 0.85, 0.9),
            ("RiskMedium", "Normal"): TrapezoidalFuzzyNumber(0.5, 0.6, 0.7, 0.8),
            ("RiskHigh", "Conservative"): TrapezoidalFuzzyNumber(0.3, 0.4, 0.5, 0.6),
            ("RiskNone", "Stop"): TrapezoidalFuzzyNumber(0.0, 0.1, 0.2, 0.3)
        }
        self.risk_levels = {
            "RiskNone": 0.0,
            "RiskLow": 0.3,
            "RiskMedium": 0.6,
            "RiskHigh": 0.9
        }
        self.action_levels = {
            "Stop": 0.0,
            "Conservative": 0.3,
            "Normal": 0.6,
            "Aggressive": 0.9
        }
        self.q = 2

    def fuzzify(self, stability, volatility, profit):
        s_membership = {k: f.membership(stability) for k, f in self.stability_sets.items()}
        v_membership = {k: f.membership(volatility) for k, f in self.volatility_sets.items()}
        p_membership = {k: f.membership(profit) for k, f in self.profit_sets.items()}
        return s_membership, v_membership, p_membership

    def infer(self, s_mem, v_mem, p_mem):
        """增强的模糊推理，集成动态规则和多源信息融合"""
        activated_rules = []

        # 使用静态规则
        for rule in self.rules:
            strength = rule["condition"](s_mem, v_mem, p_mem)
            if strength > 0:
                action = rule["action"]()
                activated_rules.append((action, strength, 'static'))

        # 使用动态规则
        if self.adaptation_enabled:
            dynamic_rules = self.dynamic_rules.get_active_rules()
            for rule in dynamic_rules:
                try:
                    strength = rule["condition"](s_mem, v_mem, p_mem)
                    if strength > 0:
                        action = rule["action"]()
                        # 动态规则权重调整
                        weighted_strength = strength * rule['weight']
                        activated_rules.append((action, weighted_strength, 'dynamic'))

                        # 更新规则使用计数
                        self.dynamic_rules.rule_usage_count[rule['id']] += 1
                        rule['last_used'] = datetime.now()
                except Exception as e:
                    print(f"动态规则执行失败: {e}")

        if not activated_rules:
            return ("RiskMedium", "Normal", 0.5)

        # 使用改进的去模糊化方法
        risk_values = []
        action_values = []
        confidence_values = []
        weights = []

        for (risk, action, conf), strength, rule_type in activated_rules:
            matrix_value = self.risk_action_matrix.get(
                (risk, action),
                TrapezoidalFuzzyNumber(0.4, 0.5, 0.6, 0.7)
            ).centroid()

            risk_values.append(self._risk_to_value(risk))
            action_values.append(self._action_to_value(action))
            confidence_values.append(conf)
            weights.append(strength)

        # 使用重心法去模糊化
        risk_value = self.defuzzifier.centroid_method(weights, risk_values)
        action_value = self.defuzzifier.centroid_method(weights, action_values)
        confidence_value = self.defuzzifier.centroid_method(weights, confidence_values)

        risk_level = self._value_to_risk(risk_value)
        action_level = self._value_to_action(action_value)

        return (risk_level, action_level, confidence_value)

    def adaptive_inference(self, s_mem, v_mem, p_mem, market_data=None):
        """自适应模糊推理，结合时间序列预测和情绪分析"""
        # 基础推理
        base_result = self.infer(s_mem, v_mem, p_mem)

        if market_data is None:
            return base_result

        try:
            # 时间序列预测增强
            if 'price_history' in market_data and len(market_data['price_history']) > 20:
                price_prediction = self.time_series_predictor.predict(
                    market_data['price_history'][-1], steps=1
                )[0]

                current_price = market_data['price_history'][-1]
                price_trend = (price_prediction - current_price) / current_price

                # 根据预测趋势调整风险等级
                if price_trend > 0.02:  # 强烈上涨预期
                    risk_adjustment = 0.1
                elif price_trend < -0.02:  # 强烈下跌预期
                    risk_adjustment = -0.1
                else:
                    risk_adjustment = 0
            else:
                risk_adjustment = 0

            # 市场情绪分析增强
            if 'sentiment_score' in market_data:
                sentiment = market_data['sentiment_score']
                if sentiment > 0.7:  # 极度贪婪
                    sentiment_adjustment = -0.15  # 降低风险偏好
                elif sentiment < 0.3:  # 极度恐惧
                    sentiment_adjustment = 0.1   # 适度提高风险偏好
                else:
                    sentiment_adjustment = 0
            else:
                sentiment_adjustment = 0

            # 综合调整
            total_adjustment = risk_adjustment + sentiment_adjustment

            # 调整风险等级
            current_risk_value = self._risk_to_value(base_result[0])
            adjusted_risk_value = np.clip(current_risk_value + total_adjustment, 0, 1)
            adjusted_risk_level = self._value_to_risk(adjusted_risk_value)

            # 调整置信度
            adjusted_confidence = min(1.0, base_result[2] + abs(total_adjustment) * 0.5)

            return (adjusted_risk_level, base_result[1], adjusted_confidence)

        except Exception as e:
            print(f"自适应推理失败，使用基础结果: {e}")
            return base_result

    def learn_from_feedback(self, input_data, decision, actual_outcome):
        """从反馈中学习，更新规则库"""
        if not self.adaptation_enabled:
            return

        try:
            # 计算决策性能
            performance_score = self._calculate_performance(decision, actual_outcome)

            # 记录学习历史
            self.learning_history.append({
                'input': input_data,
                'decision': decision,
                'outcome': actual_outcome,
                'performance': performance_score,
                'timestamp': datetime.now()
            })

            # 保持历史记录在合理范围内
            if len(self.learning_history) > 1000:
                self.learning_history.pop(0)

            # 如果性能低于阈值，触发规则学习
            if performance_score < self.performance_threshold:
                self._trigger_rule_learning()

            # 更新动态规则性能
            self._update_dynamic_rule_performance(performance_score)

        except Exception as e:
            print(f"学习过程失败: {e}")

    def _calculate_performance(self, decision, actual_outcome):
        """计算决策性能得分"""
        risk_level, action_level, confidence = decision

        # 基于实际结果计算性能
        if actual_outcome > 0:  # 盈利
            if risk_level in ["RiskMedium", "RiskHigh"] and action_level in ["Normal", "Aggressive"]:
                return 0.8 + min(0.2, actual_outcome * 10)
            else:
                return 0.6
        else:  # 亏损
            if risk_level in ["RiskNone", "RiskLow"] and action_level in ["Stop", "Conservative"]:
                return 0.7 + min(0.3, abs(actual_outcome) * 5)
            else:
                return 0.3 - min(0.3, abs(actual_outcome) * 10)

    def _trigger_rule_learning(self):
        """触发规则学习过程"""
        if len(self.learning_history) < 20:
            return

        # 提取最近的学习数据
        recent_data = self.learning_history[-20:]

        # 准备训练数据
        X = []
        y = []

        for record in recent_data:
            input_features = self._extract_features_from_input(record['input'])
            output_class = self._decision_to_class(record['decision'])

            X.append(input_features)
            y.append(output_class)

        # 使用增量学习更新规则库
        if len(X) > 5:
            self.dynamic_rules.incremental_learning(np.array(X), np.array(y))

    def _extract_features_from_input(self, input_data):
        """从输入数据提取特征向量"""
        s_mem, v_mem, p_mem = input_data

        features = [
            s_mem.get('Low', 0), s_mem.get('Medium', 0), s_mem.get('High', 0),
            v_mem.get('Low', 0), v_mem.get('Medium', 0), v_mem.get('High', 0),
            p_mem.get('Negative', 0), p_mem.get('Low', 0),
            p_mem.get('Medium', 0), p_mem.get('High', 0)
        ]

        return features

    def _decision_to_class(self, decision):
        """将决策转换为类别标签"""
        risk_level, action_level, _ = decision

        risk_map = {"RiskNone": 0, "RiskLow": 1, "RiskMedium": 2, "RiskHigh": 3}
        action_map = {"Stop": 0, "Conservative": 1, "Normal": 2, "Aggressive": 3}

        # 组合风险和行动等级
        risk_class = risk_map.get(risk_level, 2)
        action_class = action_map.get(action_level, 2)

        return risk_class * 4 + action_class  # 创建组合类别

    def _update_dynamic_rule_performance(self, performance_score):
        """更新动态规则性能"""
        # 为最近使用的规则更新性能
        current_time = datetime.now()

        for rule in self.dynamic_rules.rules:
            if rule['last_used'] and (current_time - rule['last_used']).seconds < 300:  # 5分钟内使用过
                self.dynamic_rules.update_rule_performance(rule['id'], performance_score)

    def _risk_to_value(self, risk):
        return self.risk_levels[risk]

    def _action_to_value(self, action):
        return self.action_levels[action]

    def _value_to_risk(self, value):
        if value < 0.15:
            return "RiskNone"
        elif value < 0.45:
            return "RiskLow"
        elif value < 0.75:
            return "RiskMedium"
        else:
            return "RiskHigh"

    def _value_to_action(self, value):
        if value < 0.15:
            return "Stop"
        elif value < 0.45:
            return "Conservative"
        elif value < 0.75:
            return "Normal"
        else:
            return "Aggressive"

    def dynamic_weight(self, μ, _ν, π):
        """基于偏好度的动态权重"""
        return (μ**self.q + μ**self.q * π**self.q)**(1/self.q)

    def q_rofs_correlation(self, μ1, μ2, ν1, ν2, π1, π2):
        """q-ROFS相关性计算"""
        q = self.q
        # 计算q-ROFS相关性系数
        numerator = μ1**q * μ2**q + ν1**q * ν2**q + π1**q * π2**q
        denominator = math.sqrt((μ1**q + ν1**q + π1**q) * (μ2**q + ν2**q + π2**q))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator

class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    tech_type: Literal["STC_HULL"] = Field(default="STC_HULL", title="技术指标")
    trade_direction: Literal["buy", "sell", "auto"] = Field(default="auto", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位") 
    order_volume: int = Field(default=1, title="报单数量")
    trail_profit_start: float = Field(default=0.03, title="追踪止盈启动(3%)")
    trail_profit_stop: float = Field(default=0.01, title="追踪回撤平仓(1%)")
    quick_stop_loss: float = Field(default=0.02, title="快速止损(2%)")
    volatility_threshold: float = Field(default=0.05, title="波动率阈值")
    stability_margin: float = Field(default=0.7, title="稳定裕度")
    N1: int = Field(default=9, title="HULL周期参数1")
    P1: int = Field(default=9, title="HULL周期参数2")

class State(BaseState):
    """状态映射模型"""
    stc_value: float = Field(default=0, title="STC值")
    stc_signal: float = Field(default=0, title="STC信号线")
    hull_value: float = Field(default=0, title="HULL值")
    hull_prev: float = Field(default=0, title="HULL前值")
    position_cost: float = Field(default=0.0, title="持仓成本价")
    max_profit: float = Field(default=0.0, title="最高盈利比例")
    stop_triggered: bool = Field(default=False, title="止损触发标志")
    system_stability: float = Field(default=1.0, title="系统稳定指数")
    filtered_price: float = Field(default=0.0, title="滤波后价格")
    volatility_index: float = Field(default=0.0, title="波动率指数")
    lyapunov_value: float = Field(default=0.0, title="李雅普诺夫值")
    fuzzy_risk: str = Field(default="RiskMedium", title="模糊风险等级")  # 修改默认值
    fuzzy_action: str = Field(default="Normal", title="模糊行动级别")
    fuzzy_confidence: float = Field(default=0.5, title="模糊决策置信度")
    ma1: float = Field(default=0.0, title="HULL移动平均1")
    ma0: float = Field(default=0.0, title="HULL移动平均0")
    bup: float = Field(default=0.0, title="HULL上轨")
    bdn: float = Field(default=0.0, title="HULL下轨")
    fast_line: float = Field(default=0.0, title="STC快线")
    slow_line: float = Field(default=0.0, title="STC慢线")
    cycle_length: int = Field(default=10, title="STC周期长度")

    # 机器学习预测状态
    ml_direction: float = Field(default=0.0, title="ML预测方向")
    ml_confidence: float = Field(default=0.5, title="ML预测置信度")

    # 风险管理状态
    current_var: float = Field(default=0.02, title="当前VaR")
    current_es: float = Field(default=0.03, title="当前期望损失")
    optimal_position_size: float = Field(default=1.0, title="最优仓位大小")

    # 添加缺失的属性
    current_price: float = Field(default=0.0, title="当前价格")
    sma_5: float = Field(default=0.0, title="5周期移动平均")
    price_change_rate: float = Field(default=0.0, title="价格变化率")
    tick_count: int = Field(default=0, title="Tick计数")
    ml_prediction: str = Field(default="hold", title="ML预测")

class Strategy3(BaseStrategy):
    """重构后的统一交易策略 - 高内聚低耦合架构"""

    def __init__(self):
        super().__init__()

        # 初始化配置系统
        self.config = config_manager.config

        # 初始化核心交易引擎
        self.trading_engine = TradingStrategyEngine(self.config)

        # 初始化数据管理
        self.price_history: List[float] = []
        self.volume_history: List[float] = []
        self.return_history: List[float] = []

        # 初始化状态管理
        self.tick_count = 0
        self.last_update = datetime.now()
        self.last_complex_calc = datetime.now()
        self.max_drawdown = 0.0

        # 初始化异步处理
        self.decision_queue = asyncio.Queue()
        self.action_queue = asyncio.Queue()

        # 初始化其他组件
        self.tick = None
        self.kline_generator = None
        self.order_id = None
        self.signal_price = 0.0

        # 初始化模块组件
        self.ml_predictor = None
        self.risk_manager = None
        self.control_center = None

        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False

        # 技术指标
        self.ma00 = 0
        self.ma10 = 0
        self.kk = 0
        self.dd = 0
        self.jj = 0
        self.tr = 0
        self.macd1 = 0
        self.signall1 = 0

        # 系统状态
        self.stability_counter = 0
        self.last_profit = 0.0
        self.last_complex_calc = datetime.now()

        # 性能统计
        self.trade_count = 0
        self.win_count = 0
        self.total_pnl = 0.0
        self.peak_value = 0.0

        # 初始化状态映射（兼容性）
        self.state_map = type('StateMap', (), {
            'fuzzy_risk': 'RiskMedium',
            'fuzzy_action': 'Normal',
            'fuzzy_confidence': 0.5,
            'system_stability': 1.0,
            'volatility_index': 0.5,
            'last_action': 'hold',
            'filtered_price': 0.0,
            'ml_direction': 0.0,
            'ml_confidence': 0.5,
            'current_var': 0.0,
            'current_es': 0.0,
            'ma1': 0.0,
            'ma0': 0.0,
            'bup': 0.0,
            'bdn': 0.0,
            'fast_line': 0.0,
            'slow_line': 0.0,
            'cycle_length': 0.0
        })()

        self.params_map = type('ParamsMap', (), {
            'exchange': 'default',
            'instrument_id': 'default',
            'order_volume': 1,
            'stability_margin': 0.7,
            'kline_style': 'KLINE_1MIN',
            'N1': 20,
            'P1': 10
        })()

        # 性能监控
        self.performance_metrics = {
            'total_trades': 0,
            'successful_trades': 0,
            'total_return': 0.0,
            'max_drawdown': 0.0,
            'last_performance_update': datetime.now()
        }

    def output(self, message: str) -> None:
        """输出消息的方法"""
        print(f"[Strategy3] {message}")

    def get_position(self, instrument_id: str):
        """获取持仓信息的方法"""
        # 返回一个模拟的持仓对象
        class MockPosition:
            def __init__(self, trading_engine):
                self.net_position = trading_engine.current_state.get('position', 0.0)
        return MockPosition(self.trading_engine)

    def auto_close_position(self, exchange: str, instrument_id: str, price: float, volume: float, order_direction: str = None) -> None:
        """自动平仓方法"""
        self.output(f"执行自动平仓: {exchange} {instrument_id} 价格:{price} 数量:{volume}")

        # 更新交易引擎状态
        self.trading_engine.current_state['position'] = 0.0
        return f"close_order_{self.tick_count}"

    def send_order(self, exchange: str, instrument_id: str, volume: float, price: float, order_direction: str) -> str:
        """发送订单方法"""
        self.output(f"发送订单: {exchange} {instrument_id} 数量:{volume} 价格:{price} 方向:{order_direction}")
        # 这里可以添加实际的下单逻辑
        return f"order_{self.tick_count}"

    def execute_fuzzy_decision(self) -> Dict[str, Any]:
        """执行统一的交易决策"""
        try:
            # 准备市场数据
            market_data = self._prepare_market_data()

            # 使用统一交易引擎进行决策
            decision = self.trading_engine.make_trading_decision(market_data)

            # 执行决策
            self._execute_decision(decision)

            # 更新性能指标
            self._update_performance_metrics(decision, market_data)

            return decision

        except Exception as e:
            self.output(f"交易决策执行失败: {e}")
            return self._get_safe_decision()

    def _prepare_market_data(self) -> Dict[str, Any]:
        """准备市场数据"""
        market_data = {}

        # 基础价格数据
        if hasattr(self.state_map, 'filtered_price') and self.state_map:
            market_data['price'] = self.state_map.filtered_price
            market_data['prev_price'] = self.price_history[-1] if self.price_history else market_data['price']
        else:
            market_data['price'] = 0.0
            market_data['prev_price'] = 0.0

        # 历史数据
        market_data['price_history'] = self.price_history.copy()
        market_data['volume_history'] = self.volume_history.copy()
        market_data['return_history'] = self.return_history.copy()

        # 计算收益率
        if len(self.return_history) > 0:
            market_data['returns'] = self.return_history[-1]
        else:
            market_data['returns'] = 0.0

        # 计算当前盈利
        if hasattr(self.state_map, 'position_cost') and self.state_map and self.state_map.position_cost > 0:
            current_profit = (market_data['price'] - self.state_map.position_cost) / self.state_map.position_cost
            market_data['current_profit'] = current_profit
        else:
            market_data['current_profit'] = 0.0

        # 添加技术指标
        market_data.update(self._calculate_technical_indicators())

        return market_data

    def _calculate_technical_indicators(self) -> Dict[str, float]:
        """计算技术指标"""
        indicators = {}

        if len(self.price_history) >= 5:
            prices = np.array(self.price_history[-20:])  # 最近20个价格

            # 移动平均
            if len(prices) >= 5:
                indicators['moving_avg_5'] = float(np.mean(prices[-5:]))
            if len(prices) >= 10:
                indicators['moving_avg_10'] = float(np.mean(prices[-10:]))
            if len(prices) >= 20:
                indicators['moving_avg_20'] = float(np.mean(prices[-20:]))

            # RSI (简化版)
            if len(prices) >= 14:
                deltas = np.diff(prices)
                gains = np.where(deltas > 0, deltas, 0)
                losses = np.where(deltas < 0, -deltas, 0)
                avg_gain = np.mean(gains[-14:])
                avg_loss = np.mean(losses[-14:])
                if avg_loss != 0:
                    rs = avg_gain / avg_loss
                    indicators['rsi'] = float(100 - (100 / (1 + rs)))
                else:
                    indicators['rsi'] = 100.0

            # 波动率
            if len(prices) >= 2:
                returns = np.diff(prices) / prices[:-1]
                indicators['volatility'] = float(np.std(returns))

        # 成交量指标
        if len(self.volume_history) >= 5:
            volumes = np.array(self.volume_history[-10:])
            indicators['volume_avg'] = float(np.mean(volumes))
            indicators['volume_ratio'] = float(volumes[-1] / np.mean(volumes[:-1])) if len(volumes) > 1 else 1.0

        return indicators

    def _execute_decision(self, decision: Dict[str, Any]) -> None:
        """执行交易决策"""
        action = decision.get('action', 'hold')
        position_size = decision.get('position_size', 0.0)
        confidence = decision.get('confidence', 0.5)

        self.output(f"执行决策: {action}, 仓位: {position_size:.4f}, 置信度: {confidence:.4f}")

        # 这里可以添加实际的交易执行逻辑
        # 例如：调用交易API、更新持仓等

        # 更新内部状态
        if hasattr(self.state_map, 'last_action'):
            self.state_map.last_action = action

        # 记录交易
        self.performance_metrics['total_trades'] += 1

    def _update_performance_metrics(self, decision: Dict[str, Any], market_data: Dict[str, Any]) -> None:
        """更新性能指标"""
        # 更新收益
        if 'returns' in market_data:
            self.performance_metrics['total_return'] += market_data['returns']

        # 更新最大回撤
        if 'current_profit' in market_data:
            current_profit = market_data['current_profit']
            if current_profit < 0:
                self.max_drawdown = min(self.max_drawdown, current_profit)
                self.performance_metrics['max_drawdown'] = self.max_drawdown

        # 更新成功交易数（简化判断）
        if decision.get('confidence', 0) > 0.7:
            self.performance_metrics['successful_trades'] += 1

        self.performance_metrics['last_performance_update'] = datetime.now()

    def _get_safe_decision(self) -> Dict[str, Any]:
        """获取安全的默认决策"""
        return {
            'action': 'hold',
            'confidence': 0.5,
            'position_size': 0.0,
            'is_safe_mode': True,
            'risk_assessment': {
                'risk_level': 'Medium',
                'recommendations': ['系统异常，建议保持观望']
            }
        }

    def update_market_data(self, price: float, volume: float = 0.0) -> None:
        """更新市场数据"""
        # 更新价格历史
        self.price_history.append(price)
        if len(self.price_history) > 1000:  # 限制历史数据大小
            self.price_history = self.price_history[-500:]

        # 更新成交量历史
        if volume > 0:
            self.volume_history.append(volume)
            if len(self.volume_history) > 1000:
                self.volume_history = self.volume_history[-500:]

        # 计算收益率
        if len(self.price_history) >= 2:
            returns = (self.price_history[-1] - self.price_history[-2]) / self.price_history[-2]
            self.return_history.append(returns)
            if len(self.return_history) > 1000:
                self.return_history = self.return_history[-500:]

        # 更新tick计数
        self.tick_count += 1
        self.last_update = datetime.now()

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            'strategy_info': {
                'name': 'Strategy3',
                'version': '2.0',
                'last_update': self.last_update,
                'tick_count': self.tick_count
            },
            'market_data_status': {
                'price_history_size': len(self.price_history),
                'volume_history_size': len(self.volume_history),
                'return_history_size': len(self.return_history)
            },
            'performance_metrics': self.performance_metrics,
            'trading_engine_status': self.trading_engine.get_system_status(),
            'config_summary': config_manager.get_config_summary()
        }

    def optimize_parameters(self, optimization_data: Dict[str, Any]) -> Dict[str, Any]:
        """优化策略参数"""
        try:
            self.output("开始参数优化...")

            # 这里可以添加参数优化逻辑
            # 例如：贝叶斯优化、遗传算法等

            optimization_result = {
                'status': 'completed',
                'optimized_parameters': {
                    'learning_rate': 0.01,
                    'risk_threshold': 0.05,
                    'position_size_multiplier': 0.8
                },
                'performance_improvement': 0.05,
                'optimization_time': datetime.now()
            }

            self.output("参数优化完成")
            return optimization_result

        except Exception as e:
            self.output(f"参数优化失败: {e}")
            return {'status': 'failed', 'error': str(e)}

    def train_models(self, training_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """训练模型"""
        try:
            self.output("开始模型训练...")

            training_results = []

            for data_point in training_data:
                if 'market_data' in data_point and 'actual_outcome' in data_point:
                    # 更新模型
                    self.trading_engine.update_models(
                        data_point['market_data'],
                        data_point['actual_outcome']
                    )
                    training_results.append({
                        'timestamp': datetime.now(),
                        'outcome': data_point['actual_outcome']
                    })

            self.output(f"模型训练完成，训练样本数: {len(training_results)}")

            return {
                'status': 'completed',
                'training_samples': len(training_results),
                'training_time': datetime.now(),
                'model_performance': self.trading_engine.get_system_status()['performance_metrics']
            }

        except Exception as e:
            self.output(f"模型训练失败: {e}")
            return {'status': 'failed', 'error': str(e)}
            if len(self.price_history) > 20 and len(self.volume_history) > 20:
                sentiment_scores = self.info_fusion.extract_market_sentiment(
                    self.price_history, self.volume_history
                )
                if sentiment_scores:
                    current_sentiment = sentiment_scores[-1]
                    market_data['sentiment_score'] = current_sentiment
                    self.market_sentiment_history.append(current_sentiment)

                    # 保持历史记录在合理范围内
                    if len(self.market_sentiment_history) > 500:
                        self.market_sentiment_history.pop(0)

            # 模糊时间序列预测
            if len(self.price_history) > 30:
                try:
                    # 训练时间序列预测器
                    if not hasattr(self.time_series_predictor, 'rules') or not self.time_series_predictor.rules:
                        self.time_series_predictor.fit(self.price_history[-100:])

                    # 预测未来价格
                    price_predictions = self.time_series_predictor.predict(
                        self.price_history[-1], steps=3
                    )
                    market_data['price_predictions'] = price_predictions

                except Exception as e:
                    self.output(f"时间序列预测失败: {e}")

            # 执行自适应模糊决策
            fuzzy_decision = self.control_center.adaptive_trapezoidal_fuzzy_decision(
                self.state_map.system_stability,
                self.state_map.volatility_index,
                current_profit,
                market_data
            )

            # 获取ML预测
            ml_prediction = self._get_ml_prediction()

            # 通过接口融合模糊决策和ML预测
            try:
                fusion_data = {
                    'fuzzy_output': {
                        'action': str(fuzzy_decision[1]) if len(fuzzy_decision) > 1 else 'hold',
                        'confidence': float(fuzzy_decision[2]) if len(fuzzy_decision) > 2 else 0.5,
                        'risk_level': str(fuzzy_decision[0]) if len(fuzzy_decision) > 0 else 'RiskMedium'
                    },
                    'ml_prediction': ml_prediction,
                    'market_data': market_data
                }

                fused_decision = self.fuzzy_ml_interface.process(fusion_data)

                # 使用融合后的决策
                final_decision = (
                    fuzzy_decision[0] if len(fuzzy_decision) > 0 else 'RiskMedium',
                    fused_decision.get('action', 'hold'),
                    fused_decision.get('confidence', 0.5)
                )

            except Exception as e:
                self.output(f"决策融合失败，使用原始模糊决策: {e}")
                final_decision = fuzzy_decision

            # 风险管理接口检查
            try:
                position = self.get_position(self.params_map.instrument_id)
                risk_data = {
                    'position': position.net_position if position else 0,
                    'price': self.state_map.filtered_price,
                    'volatility': self.state_map.volatility_index,
                    'var': self._calculate_current_var()
                }

                risk_advice = self.risk_decision_interface.process(risk_data)

                # 根据风险建议调整决策
                if risk_advice.get('urgency') == 'high':
                    final_decision = ('RiskHigh', 'reduce_position', 0.9)
                elif risk_advice.get('urgency') == 'medium':
                    # 降低置信度
                    final_decision = (final_decision[0], final_decision[1], final_decision[2] * 0.8)

            except Exception as e:
                self.output(f"风险管理检查失败: {e}")

            # 应用最终决策到状态
            if isinstance(final_decision, tuple) and len(final_decision) >= 3:
                old_decision = (self.state_map.fuzzy_risk, self.state_map.fuzzy_action, self.state_map.fuzzy_confidence)

                self.state_map.fuzzy_risk = str(final_decision[0])
                self.state_map.fuzzy_action = str(final_decision[1])
                self.state_map.fuzzy_confidence = float(final_decision[2])

                # 记录决策变化
                if old_decision != final_decision:
                    self.output(f"模糊决策更新: {old_decision} -> {final_decision}")

            # 模糊聚类规则提取（每天执行一次）
            if datetime.now().hour == 0 and len(self.fuzzy_learning_data) > 50:
                self.extract_fuzzy_rules_from_clustering()

            # 每周执行自适应规则更新
            if datetime.now().weekday() == 0:  # 每周一
                self.control_center.adaptive_rule_update()
                self.evaluate_fuzzy_system_performance()

            # 更新状态栏
            self.update_status_bar()

        except Exception as e:
            self.output(f"模糊决策执行失败: {e}")
            # 使用默认决策
            self.state_map.fuzzy_risk = "RiskMedium"
            self.state_map.fuzzy_action = "Normal"
            self.state_map.fuzzy_confidence = 0.5

    def extract_fuzzy_rules_from_clustering(self):
        """使用模糊聚类提取新规则"""
        try:
            if len(self.fuzzy_learning_data) < 20:
                return

            # 准备聚类数据
            features = []
            labels = []

            for record in self.fuzzy_learning_data[-100:]:  # 使用最近100条记录
                if 'features' in record and 'outcome' in record:
                    features.append(record['features'])
                    # 将结果转换为类别标签
                    outcome = record['outcome']
                    if outcome > 0.02:
                        labels.append(2)  # 高收益
                    elif outcome > 0:
                        labels.append(1)  # 低收益
                    else:
                        labels.append(0)  # 亏损

            if len(features) < 10:
                return

            # 执行模糊聚类
            X = np.array(features)
            y = np.array(labels)

            # 提取模糊规则
            fuzzy_rules = self.fuzzy_clustering.extract_fuzzy_rules(X, y)

            # 将规则添加到动态规则库
            for rule_info in fuzzy_rules:
                if rule_info['confidence'] > 0.6:  # 只添加高置信度规则
                    self.control_center.advanced_fuzzy_system.dynamic_rules.add_rule(
                        self._create_condition_from_cluster(rule_info),
                        self._create_action_from_cluster(rule_info),
                        rule_info['confidence']
                    )

            self.output(f"从聚类中提取了 {len(fuzzy_rules)} 个模糊规则")

        except Exception as e:
            self.output(f"模糊聚类规则提取失败: {e}")

    def _create_condition_from_cluster(self, rule_info):
        """从聚类信息创建条件函数"""
        center = rule_info['features']['center']
        std = rule_info['features']['std']

        def condition(s_mem, v_mem, p_mem):
            try:
                # 构建输入向量
                input_vector = np.array([
                    s_mem.get('Low', 0), s_mem.get('Medium', 0), s_mem.get('High', 0),
                    v_mem.get('Low', 0), v_mem.get('Medium', 0), v_mem.get('High', 0),
                    p_mem.get('Negative', 0), p_mem.get('Low', 0),
                    p_mem.get('Medium', 0), p_mem.get('High', 0)
                ])

                # 计算与聚类中心的相似度
                if len(input_vector) <= len(center):
                    distance = np.linalg.norm(input_vector - center[:len(input_vector)])
                    threshold = np.mean(std[:len(input_vector)]) * 1.5
                    similarity = np.exp(-distance / (threshold + 1e-6))
                    return similarity > 0.5
                return False
            except:
                return False

        return condition

    def _create_action_from_cluster(self, rule_info):
        """从聚类信息创建动作函数"""
        output_class = rule_info['output']
        confidence = rule_info['confidence']

        def action():
            if output_class == 2:  # 高收益预期
                return ("RiskHigh", "Aggressive", confidence)
            elif output_class == 1:  # 低收益预期
                return ("RiskMedium", "Normal", confidence)
            else:  # 亏损预期
                return ("RiskLow", "Conservative", confidence)

        return action

    def evaluate_fuzzy_system_performance(self):
        """评估模糊系统性能"""
        try:
            if len(self.prediction_accuracy_history) < 10:
                return

            # 计算预测准确率
            recent_accuracy = np.mean(self.prediction_accuracy_history[-50:])

            # 计算决策效果
            if len(self.fuzzy_learning_data) > 20:
                recent_outcomes = [record['outcome'] for record in self.fuzzy_learning_data[-20:]]
                avg_outcome = np.mean(recent_outcomes)
                win_rate = sum(1 for outcome in recent_outcomes if outcome > 0) / len(recent_outcomes)

                self.output(f"模糊系统性能评估 - 预测准确率: {recent_accuracy:.2%}, 平均收益: {avg_outcome:.4f}, 胜率: {win_rate:.2%}")

                # 如果性能不佳，调整学习参数
                if recent_accuracy < 0.6 or win_rate < 0.5:
                    self.control_center.advanced_fuzzy_system.adaptation_enabled = True
                    self.control_center.advanced_fuzzy_system.performance_threshold = 0.7
                    self.output("检测到性能下降，启用增强学习模式")

        except Exception as e:
            self.output(f"性能评估失败: {e}")

    def _setup_config_watchers(self):
        """设置配置变化监听器"""
        if not self.config_manager:
            return

        def on_config_change(key_path, value):
            """配置变化回调"""
            self.output(f"配置更新: {key_path} = {value}")

            # 根据配置变化更新系统参数
            if key_path.startswith('learning_parameters'):
                self._update_learning_parameters()
            elif key_path.startswith('fuzzy_sets'):
                self._update_fuzzy_sets()
            elif key_path.startswith('fusion_weights'):
                self._update_fusion_weights()

        self.config_manager.watch(on_config_change)

        # 启动文件监控（如果方法存在）
        if hasattr(self.config_manager, 'start_file_watcher'):
            self.config_manager.start_file_watcher()

    def _update_learning_parameters(self):
        """更新学习参数"""
        if not self.config_manager:
            return

        try:
            learning_rate = self.config_manager.get('learning_parameters.learning_rate', 0.01)
            adaptation_enabled = self.config_manager.get('learning_parameters.adaptation_enabled', True)
            max_rules = self.config_manager.get('learning_parameters.max_rules', 30)

            # 更新动态规则库参数
            if hasattr(self.control_center.advanced_fuzzy_system, 'dynamic_rules'):
                if isinstance(learning_rate, (int, float)):
                    self.control_center.advanced_fuzzy_system.dynamic_rules.learning_rate = float(learning_rate)
                if isinstance(max_rules, (int, float)):
                    self.control_center.advanced_fuzzy_system.dynamic_rules.max_rules = int(max_rules)
                if isinstance(adaptation_enabled, bool):
                    self.control_center.advanced_fuzzy_system.adaptation_enabled = adaptation_enabled

            self.output(f"学习参数已更新: lr={learning_rate}, enabled={adaptation_enabled}, max_rules={max_rules}")

        except Exception as e:
            self.output(f"学习参数更新失败: {e}")

    def _update_fuzzy_sets(self):
        """更新模糊集定义"""
        if not self.config_manager:
            return

        try:
            # 这里可以根据配置更新模糊集
            # 由于模糊集结构复杂，这里只记录变化
            self.output("模糊集配置已更新，将在下次决策时生效")

        except Exception as e:
            self.output(f"模糊集更新失败: {e}")

    def _update_fusion_weights(self):
        """更新信息融合权重"""
        if not self.config_manager:
            return

        try:
            fusion_weights = self.config_manager.get('fusion_weights', {})

            if hasattr(self.info_fusion, 'fusion_weights') and isinstance(fusion_weights, dict):
                # 安全地更新融合权重
                for key, value in fusion_weights.items():
                    if isinstance(value, (int, float)):
                        self.info_fusion.fusion_weights[key] = float(value)
                self.output(f"信息融合权重已更新: {fusion_weights}")

        except Exception as e:
            self.output(f"融合权重更新失败: {e}")

    def generate_fuzzy_visualization(self, viz_type="all"):
        """生成模糊系统可视化"""
        if not self.visualizer:
            self.output("可视化模块不可用")
            return None

        try:
            visualizations = {}

            if viz_type in ["all", "membership"]:
                # 隶属函数可视化
                stability_sets = self.control_center.advanced_fuzzy_system.stability_sets
                volatility_sets = self.control_center.advanced_fuzzy_system.volatility_sets
                profit_sets = self.control_center.advanced_fuzzy_system.profit_sets

                visualizations['stability_membership'] = self.visualizer.plot_membership_functions(
                    stability_sets, "稳定性隶属函数"
                )
                visualizations['volatility_membership'] = self.visualizer.plot_membership_functions(
                    volatility_sets, "波动率隶属函数"
                )
                visualizations['profit_membership'] = self.visualizer.plot_membership_functions(
                    profit_sets, "盈利隶属函数"
                )

            if viz_type in ["all", "rules"] and hasattr(self.control_center.advanced_fuzzy_system, 'dynamic_rules'):
                # 规则性能可视化
                rule_performance_data = {}
                for rule in self.control_center.advanced_fuzzy_system.dynamic_rules.rules:
                    rule_id = rule['id']
                    rule_performance_data[rule_id] = {
                        'usage_count': self.control_center.advanced_fuzzy_system.dynamic_rules.rule_usage_count.get(rule_id, 0),
                        'performance_history': self.control_center.advanced_fuzzy_system.dynamic_rules.rule_performance.get(rule_id, []),
                        'current_weight': rule['weight']
                    }

                if rule_performance_data:
                    visualizations['rule_performance'] = self.visualizer.plot_rule_performance(
                        rule_performance_data, "动态规则性能分析"
                    )

            if viz_type in ["all", "prediction"] and len(self.price_history) > 30:
                # 时间序列预测可视化
                recent_prices = self.price_history[-50:]
                if hasattr(self.time_series_predictor, 'predict'):
                    try:
                        predictions = self.time_series_predictor.predict(recent_prices[-1], steps=5)
                        visualizations['time_series'] = self.visualizer.plot_time_series_prediction(
                            recent_prices, predictions, "模糊时间序列预测"
                        )
                    except:
                        pass

            # 缓存可视化结果
            self.visualization_cache.update(visualizations)

            self.output(f"生成了 {len(visualizations)} 个可视化图表")
            return visualizations

        except Exception as e:
            self.output(f"可视化生成失败: {e}")
            return None

    def export_fuzzy_config(self, filename=None):
        """导出模糊系统配置"""
        if not self.config_manager:
            self.output("配置管理器不可用")
            return False

        try:
            if filename is None:
                filename = f"fuzzy_config_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"

            if hasattr(self.config_manager, 'export_config'):
                self.config_manager.export_config(filename)
                self.output(f"配置已导出到: {filename}")
                return True
            else:
                self.output("配置导出功能不可用")
                return False

        except Exception as e:
            self.output(f"配置导出失败: {e}")
            return False

    def import_fuzzy_config(self, filename):
        """导入模糊系统配置"""
        if not self.config_manager:
            self.output("配置管理器不可用")
            return False

        try:
            if hasattr(self.config_manager, 'import_config'):
                self.config_manager.import_config(filename)
                self.output(f"配置已从 {filename} 导入")
                return True
            else:
                self.output("配置导入功能不可用")
                return False

        except Exception as e:
            self.output(f"配置导入失败: {e}")
            return False

    def get_fuzzy_system_status(self):
        """获取模糊系统状态"""
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'basic_info': {
                    'fuzzy_risk': self.state_map.fuzzy_risk,
                    'fuzzy_action': self.state_map.fuzzy_action,
                    'fuzzy_confidence': self.state_map.fuzzy_confidence,
                    'system_stability': self.state_map.system_stability,
                    'volatility_index': self.state_map.volatility_index
                },
                'learning_data': {
                    'total_records': len(self.fuzzy_learning_data),
                    'sentiment_history_length': len(self.market_sentiment_history),
                    'prediction_accuracy_length': len(self.prediction_accuracy_history)
                }
            }

            # 动态规则库状态
            if hasattr(self.control_center.advanced_fuzzy_system, 'dynamic_rules'):
                rule_stats = self.control_center.advanced_fuzzy_system.dynamic_rules.get_rule_statistics()
                status['dynamic_rules'] = rule_stats

            # 配置状态
            if self.config_manager:
                config_summary = self.config_manager.get_config_summary()
                status['config'] = config_summary

            return status

        except Exception as e:
            self.output(f"获取系统状态失败: {e}")
            return None



    def execute_close_position(self, position, direction):
        """执行平仓操作"""
        if self.tick:
            price = self.tick.bid_price1 if direction == "sell" else self.tick.ask_price1
            self.signal_price = -price if direction == "sell" else price
            self.order_id = self.auto_close_position(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                price=price,
                volume=abs(position.net_position),
                order_direction=direction
            )

    def execute_open_position(self, direction, volume):
        """执行开仓操作"""
        if self.tick:
            price = self.tick.ask_price1 if direction == "buy" else self.tick.bid_price1
            self.signal_price = price if direction == "buy" else -price
            self.order_id = self.send_order(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                volume=volume,
                price=price,
                order_direction=direction
            )

    def __init__(self):
        super().__init__()

        # ===== 核心组件初始化 =====
        self.params_map = Params()
        self.state_map = State()

        # 初始化状态跟踪
        self._initialization_status = {
            'core_components': False,
            'ml_modules': False,
            'fuzzy_modules': False,
            'risk_management': False,
            'async_processing': False,
            'signal_system': False,
            'complete': False
        }

        # 信号优先级系统
        self._signal_priority_config = {
            'emergency_stop': 100,      # 紧急停止信号
            'risk_management': 90,      # 风险管理信号
            'fuzzy_decision': 80,       # 模糊决策信号
            'ml_prediction': 70,        # ML预测信号
            'technical_signal': 60,     # 技术指标信号
            'sentiment_signal': 50      # 情绪信号
        }

        # 信号冲突解决机制
        self._signal_conflict_resolver = SignalConflictResolver(self._signal_priority_config)

        try:
            # 初始化控制中心
            self.control_center = ControlCenter(self)
            self._initialization_status['core_components'] = True

            # 机器学习和风险管理模块
            self._initialize_ml_modules()

            # 高级模糊理论模块
            self._initialize_fuzzy_modules()

            # 风险管理模块
            self._initialize_risk_management()

            # 异步处理模块
            self._initialize_async_processing()

            # 信号系统
            self._initialize_signal_system()

            # 验证初始化完整性
            self._validate_initialization()

        except Exception as e:
            self.output(f"策略初始化失败: {e}")
            raise

    def _initialize_ml_modules(self):
        """初始化机器学习模块"""
        try:
            self.ml_predictor = MLEnhancedPredictor()

            # 验证ML模块
            if not hasattr(self.ml_predictor, 'extract_features'):
                raise RuntimeError("ML预测器缺少必要方法")

            self._initialization_status['ml_modules'] = True
            self.output("ML模块初始化完成")

        except Exception as e:
            self.output(f"ML模块初始化失败: {e}")
            raise

    def _initialize_fuzzy_modules(self):
        """初始化模糊理论模块"""
        try:
            self.fuzzy_clustering = FuzzyCMeansClusterer(n_clusters=5)
            self.time_series_predictor = FuzzyTimeSeriesPredictor(window_size=20)
            self.info_fusion = MultiSourceInformationFusion()
            self.defuzzifier = AdvancedDefuzzification()

            # 模糊学习历史
            self.fuzzy_learning_data = []
            self.market_sentiment_history = []
            self.prediction_accuracy_history = []

            # 验证模糊模块
            if not hasattr(self.control_center, 'advanced_fuzzy_system'):
                raise RuntimeError("高级模糊系统未正确初始化")

            # 配置管理和可视化
            if FUZZY_MODULES_AVAILABLE:
                self.config_manager = config_manager
                self.visualizer = visualization_engine
                self.async_processor = async_fuzzy_processor
                self._setup_config_watchers()
            else:
                self.config_manager = None
                self.visualizer = None
                self.async_processor = None

            # 可视化数据缓存
            self.visualization_cache = {
                'membership_plots': {},
                'rule_performance': {},
                'decision_history': [],
                'clustering_results': {}
            }

            self._initialization_status['fuzzy_modules'] = True
            self.output("模糊理论模块初始化完成")

        except Exception as e:
            self.output(f"模糊理论模块初始化失败: {e}")
            raise

    def _initialize_risk_management(self):
        """初始化风险管理模块"""
        try:
            self.risk_manager = AdvancedRiskManager()

            # 验证风险管理模块
            if not hasattr(self.risk_manager, 'calculate_var'):
                raise RuntimeError("风险管理器缺少必要方法")

            self._initialization_status['risk_management'] = True
            self.output("风险管理模块初始化完成")

        except Exception as e:
            self.output(f"风险管理模块初始化失败: {e}")
            raise

    def _initialize_async_processing(self):
        """初始化异步处理模块"""
        try:
            if FUZZY_MODULES_AVAILABLE and self.async_processor:
                # 验证异步处理器状态
                if not self.async_processor._initialization_complete:
                    raise RuntimeError("异步处理器初始化不完整")

                self._setup_async_processing()

            self._initialization_status['async_processing'] = True
            self.output("异步处理模块初始化完成")

        except Exception as e:
            self.output(f"异步处理模块初始化失败: {e}")
            # 异步处理失败不应阻止策略启动
            self._initialization_status['async_processing'] = False

    def _initialize_signal_system(self):
        """初始化信号系统"""
        try:
            # 智能优化引擎
            self.intelligent_optimizer = IntelligentOptimizer(self)

            # 模块间接口
            self.risk_decision_interface = risk_decision_interface
            self.fuzzy_ml_interface = fuzzy_ml_interface

            # 交易信号初始化
            self.buy_signal: bool = False
            self.sell_signal: bool = False
            self.cover_signal: bool = False
            self.short_signal: bool = False

            # 信号历史和状态
            self.signal_history = []
            self.last_signal_time = 0
            self.signal_cooldown = 0.1  # 信号冷却时间(秒)

            self._initialization_status['signal_system'] = True
            self.output("信号系统初始化完成")

        except Exception as e:
            self.output(f"信号系统初始化失败: {e}")
            raise

    def _validate_initialization(self):
        """验证初始化完整性"""
        try:
            # 检查所有关键组件
            required_components = ['core_components', 'ml_modules', 'fuzzy_modules',
                                 'risk_management', 'signal_system']

            missing_components = []
            for component in required_components:
                if not self._initialization_status.get(component, False):
                    missing_components.append(component)

            if missing_components:
                raise RuntimeError(f"关键组件初始化失败: {missing_components}")

            # 标记初始化完成
            self._initialization_status['complete'] = True
            self.output("策略初始化验证通过")

        except Exception as e:
            self.output(f"初始化验证失败: {e}")
            raise

# ===== 信号冲突解决器 =====
class SignalConflictResolver:
    """信号冲突解决器"""

    def __init__(self, priority_config):
        self.priority_config = priority_config
        self.conflict_history = []
        self.resolution_stats = defaultdict(int)

    def resolve_signal_conflict(self, signals):
        """解决信号冲突"""
        if len(signals) <= 1:
            return signals[0] if signals else None

        # 按优先级排序
        sorted_signals = sorted(signals,
                              key=lambda x: self.priority_config.get(x['type'], 0),
                              reverse=True)

        # 记录冲突
        conflict_info = {
            'timestamp': datetime.now(),
            'signals': [s['type'] for s in signals],
            'resolution': sorted_signals[0]['type']
        }
        self.conflict_history.append(conflict_info)
        self.resolution_stats[sorted_signals[0]['type']] += 1

        # 特殊冲突处理逻辑
        primary_signal = sorted_signals[0]
        secondary_signal = sorted_signals[1] if len(sorted_signals) > 1 else None

        # 风险管理信号优先级最高
        if primary_signal['type'] == 'risk_management':
            return primary_signal

        # 模糊决策与ML预测冲突时的特殊处理
        if (primary_signal['type'] == 'fuzzy_decision' and
            secondary_signal and secondary_signal['type'] == 'ml_prediction'):

            # 比较置信度
            fuzzy_confidence = primary_signal.get('confidence', 0.5)
            ml_confidence = secondary_signal.get('confidence', 0.5)

            # 如果ML预测置信度显著更高，则选择ML预测
            if ml_confidence > fuzzy_confidence + 0.2:
                return secondary_signal

        return primary_signal

    def get_conflict_stats(self):
        """获取冲突统计"""
        return {
            'total_conflicts': len(self.conflict_history),
            'resolution_stats': dict(self.resolution_stats),
            'recent_conflicts': self.conflict_history[-10:] if self.conflict_history else []
        }

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """HULL主图指标"""
        if self.params_map is None or self.state_map is None:
            return {}
        return {
            f"HULL{getattr(self.params_map, 'N1', 20)}": getattr(self.state_map, 'ma1', 0.0),
            f"HULL{getattr(self.params_map, 'P1', 10)}": getattr(self.state_map, 'ma0', 0.0),
            "HULL_UPPER": getattr(self.state_map, 'bup', 0.0),
            "HULL_LOWER": getattr(self.state_map, 'bdn', 0.0)
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """STC指标数据输出（Schaff趋势周期）"""
        if self.state_map is None:
            return {}
        return {
            "STC_FAST": getattr(self.state_map, 'fast_line', 0.0),
            "STC_SLOW": getattr(self.state_map, 'slow_line', 0.0),
            "STC_CYCLE": getattr(self.state_map, 'cycle_length', 0.0)
        }

    def on_tick(self, tick: TickData):
        """Tick数据处理（优化高频调用性能）"""
        super().on_tick(tick)

        self.tick = tick

        # 检查K线生成器状态
        if self.kline_generator is None:
            self.output("警告：K线生成器未初始化，跳过tick处理")
            return

        # 节流机制：限制复杂计算频率
        current_time = time.time()
        if not hasattr(self, '_last_complex_calc_time'):
            self._last_complex_calc_time = 0
            self._tick_count = 0
            self._complex_calc_interval = 0.1  # 100ms间隔进行复杂计算

        self._tick_count += 1
        should_do_complex_calc = (current_time - self._last_complex_calc_time) >= self._complex_calc_interval

        try:
            self.kline_generator.tick_to_kline(tick)

            # 基础价格更新（每次tick都执行）
            mid_price = (tick.ask_price1 + tick.bid_price1) / 2
            self.price_history.append(mid_price)

            # 成交量更新（轻量级操作）
            if hasattr(tick, 'volume'):
                self.volume_history.append(tick.volume)
            else:
                self.volume_history.append(1000)

            # 保持历史数据在合理范围内（轻量级操作）
            if len(self.price_history) > 1000:
                self.price_history.pop(0)
            if len(self.volume_history) > 1000:
                self.volume_history.pop(0)

            # 复杂计算（节流执行）
            if should_do_complex_calc:
                self._perform_complex_calculations(mid_price)
                self._last_complex_calc_time = current_time

            # 轻量级状态更新
            self._update_basic_state(mid_price)

        except Exception as e:
            self.output(f"Tick处理异常: {e}")

    def _perform_complex_calculations(self, mid_price):
        """执行复杂计算（节流调用）"""
        try:
            # 计算收益率历史
            if len(self.price_history) >= 2:
                current_return = (self.price_history[-1] - self.price_history[-2]) / self.price_history[-2]
                self.return_history.append(current_return)
                if len(self.return_history) > 500:
                    self.return_history.pop(0)

            # 应用卡尔曼滤波
            filtered_price = self.control_center.kalman_filter(mid_price)
            self.state_map.filtered_price = filtered_price

            # 计算波动率
            if len(self.price_history) > 10:
                returns = np.diff(np.log(self.price_history[-10:]))
                volatility = np.std(returns) * math.sqrt(252)
                self.state_map.volatility_index = float(volatility)

            # 定时更新机器学习预测
            current_time = datetime.now()
            if (current_time - self.last_ml_update).seconds > 30:
                self.update_ml_predictions()
                self.last_ml_update = current_time

            # 定时风险管理检查
            if (current_time - self.last_risk_check).seconds > 60:
                self.perform_risk_checks()
                self.last_risk_check = current_time

            # 定时执行模糊决策
            if (current_time - self.last_update).seconds > 60:
                self.execute_fuzzy_decision()
                self.last_update = current_time

        except Exception as e:
            self.output(f"复杂计算失败: {e}")

    def _update_basic_state(self, mid_price):
        """更新基础状态（轻量级操作）"""
        try:
            # 更新基础价格信息
            self.state_map.current_price = mid_price

            # 更新简单移动平均
            if len(self.price_history) >= 5:
                self.state_map.sma_5 = np.mean(self.price_history[-5:])

            # 更新价格变化率
            if len(self.price_history) >= 2:
                price_change = (self.price_history[-1] - self.price_history[-2]) / self.price_history[-2]
                self.state_map.price_change_rate = price_change

            # 更新tick计数
            if not hasattr(self.state_map, 'tick_count'):
                self.state_map.tick_count = 0
            self.state_map.tick_count += 1

        except Exception as e:
            self.output(f"基础状态更新失败: {e}")

    def _get_ml_prediction(self):
        """获取ML预测"""
        try:
            if hasattr(self.state_map, 'ml_prediction') and hasattr(self.state_map, 'ml_confidence'):
                return {
                    'action': self.state_map.ml_prediction,
                    'confidence': self.state_map.ml_confidence
                }
            else:
                return {
                    'action': 'hold',
                    'confidence': 0.5
                }
        except Exception:
            return {
                'action': 'hold',
                'confidence': 0.5
            }

    def _calculate_current_var(self):
        """计算当前VaR"""
        try:
            if len(self.return_history) > 20:
                returns = np.array(self.return_history[-20:])
                var_95 = np.percentile(returns, 5)  # 95% VaR
                return float(var_95)
            else:
                return 0.0
        except Exception:
            return 0.0

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        super().on_trade(trade, log)
        self.order_id = None
        self.stability_counter += 1
        
        # 记录交易利润
        if trade.offset == "close" and self.state_map.position_cost > 0:
            self.last_profit = float((trade.price - self.state_map.position_cost) / self.state_map.position_cost)
        
        # 每5次交易检查系统稳定性
        if self.stability_counter >= 5:
            self.check_system_stability()
            self.stability_counter = 0

    def on_start(self):
        """策略启动"""
        self.output("策略初始化开始...")
        
        # 初始化K线生成器 - 采用简洁稳定的方式
        try:
            if (self.params_map and
                getattr(self.params_map, 'exchange', None) and
                getattr(self.params_map, 'instrument_id', None)):
                self.kline_generator = KLineGenerator(
                    callback=getattr(self, 'callback', None),
                    real_time_callback=getattr(self, 'real_time_callback', None),
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    style=getattr(self.params_map, 'kline_style', 'KLINE_1MIN')
                )
                self.output("K线生成器创建成功")
            else:
                self.output("交易所或合约代码为空，跳过K线生成器创建")
                self.kline_generator = None
        except Exception as e:
            self.output("K线生成器创建失败: {}".format(e))
            self.kline_generator = None
        
        # 调用父类的on_start方法
        super().on_start()
        
        # 推送历史数据
        if self.kline_generator:
            try:
                self.kline_generator.push_history_data()
                self.output("历史数据推送完成")
            except Exception as e:
                self.output("历史数据推送失败: {}".format(e))
        
        # 初始化状态
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None
        self.price_history = []
        self.stability_counter = 0
        self.last_profit = 0.0
        
        # 更新状态栏
        self.update_status_bar()
        
        self.output("策略启动完成: {}".format(self.__class__.__name__))
        self.output("交易所: {}".format(self.params_map.exchange))
        self.output("合约: {}".format(self.params_map.instrument_id))

    def on_stop(self):
        """策略停止"""
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        try:
            # 计算指标
            self.calc_indicator()
            
            # 计算信号
            self.calc_signal(kline)
            
            # 信号执行
            self.exec_signal()
            
            # 线图更新
            if hasattr(self, 'widget') and self.widget is not None:
                try:
                    self.widget.recv_kline({
                        "kline": kline,
                        "signal_price": self.signal_price,
                        **self.main_indicator_data,
                        **self.sub_indicator_data
                    })
                except Exception as e:
                    self.output(f"线图更新失败: {str(e)}")
            
            if self.trading:
                self.update_status_bar()
                
        except Exception as e:
            self.output(f"K线回调处理失败: {str(e)}")
            # 不抛出异常，保持策略运行

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        try:
            self.calc_indicator()
            
            if hasattr(self, 'widget') and self.widget is not None:
                try:
                    self.widget.recv_kline({
                        "kline": kline,
                        **self.main_indicator_data,
                        **self.sub_indicator_data
                    })
                except Exception as e:
                    self.output(f"实时线图更新失败: {str(e)}")
            
            self.update_status_bar()
            
        except Exception as e:
            self.output(f"实时K线回调处理失败: {str(e)}")
            # 不抛出异常，保持策略运行

    def check_system_stability(self):
        """综合稳定性检查"""
        # 定义系统传递函数
        def transfer_func(s):
            return (self.kline_generator.producer.ema(10)[-1] * s + 0.5) / (s**2 + 0.6*s + 0.1)
        
        # 执行稳定性检查
        is_stable = self.control_center.system_stability_check(transfer_func)
        stability_level = 1.0 if is_stable else 0.6
        
        # 考虑波动率因素
        volatility_factor = 1.0 - min(1.0, self.state_map.volatility_index / self.params_map.volatility_threshold)
        self.state_map.system_stability = stability_level * volatility_factor
        
        if self.state_map.system_stability < self.params_map.stability_margin:
            self.output(f"系统稳定性低: {self.state_map.system_stability:.2f} < {self.params_map.stability_margin}")

    def calc_stc_hull_signal(self):
        """增强信号生成"""
        # 获取模糊决策结果
        decision = self.control_center.fuzzy_decision(
            self.state_map.system_stability,
            self.state_map.volatility_index,
            self.last_profit
        )
        
        # q-ROFS相关性过滤
        corr = self.control_center.advanced_fuzzy_system.q_rofs_correlation(
            μ1=self.state_map.stc_value,
            μ2=self.state_map.hull_value,
            ν1=1-self.state_map.stc_value,
            ν2=1-self.state_map.hull_value,
            π1=0.2, π2=0.2
        )
        
        # 获取STC和Hull指标状态
        stc_bull = self.state_map.stc_value > self.state_map.stc_signal
        hull_bull = self.state_map.hull_value > self.state_map.hull_prev
        
        # 综合判断
        if decision[0] == "High" and corr > 0.8:
            self.buy_signal = stc_bull and hull_bull
            self.short_signal = not stc_bull and not hull_bull
        
        # 1. 检查止盈止损条件
        current_price = self.state_map.filtered_price
        if self.state_map.position_cost > 0:
            profit_ratio = (current_price - self.state_map.position_cost) / self.state_map.position_cost
            
            # 应用PID控制器动态调整止损参数
            error = profit_ratio - self.params_map.trail_profit_start
            adjusted_stop = self.control_center.update_pid(error)
            
            # 追踪止盈逻辑
            if profit_ratio >= self.params_map.trail_profit_start:
                self.state_map.max_profit = max(self.state_map.max_profit, profit_ratio)
                if profit_ratio < (self.state_map.max_profit - max(self.params_map.trail_profit_stop, adjusted_stop)):
                    self.cover_signal = True
                    self.sell_signal = True
                    self.state_map.stop_triggered = True
                    return
            
            # 快速止损逻辑
            if profit_ratio <= -self.params_map.quick_stop_loss:
                self.cover_signal = True
                self.sell_signal = True
                self.state_map.stop_triggered = True
                return

        # 2. 原始指标信号生成
        stc_bull = self.state_map.stc_value > self.state_map.stc_signal
        hull_bull = self.state_map.hull_value > self.state_map.hull_prev
        
        # 3. 综合信号处理 (考虑模糊决策)
        if self.state_map.fuzzy_action == "Stop":
            # 停止交易信号
            self.buy_signal = False
            self.short_signal = False
            return
            
        stability_factor = max(0.5, self.state_map.system_stability)
        
        if self.params_map.trade_direction == "auto":
            self.buy_signal = stc_bull and hull_bull and not self.state_map.stop_triggered
            self.short_signal = (not stc_bull and not hull_bull) and not self.state_map.stop_triggered
            self.cover_signal = self.buy_signal
            self.sell_signal = self.short_signal
            
            # 应用模糊行动级别
            if self.state_map.fuzzy_action == "Conservative":
                self.buy_signal = self.buy_signal and stability_factor > 0.8
                self.short_signal = self.short_signal and stability_factor > 0.8
            elif self.state_map.fuzzy_action == "Aggressive":
                self.buy_signal = self.buy_signal and stability_factor > 0.6
                self.short_signal = self.short_signal and stability_factor > 0.6
        else:
            self.buy_signal = stc_bull and hull_bull and not self.state_map.stop_triggered
            self.short_signal = (not stc_bull and not hull_bull) and not self.state_map.stop_triggered
            
            if self.params_map.trade_direction == "buy":
                self.buy_signal, self.short_signal = self.short_signal, self.buy_signal
            
            self.cover_signal = self.buy_signal
            self.sell_signal = self.short_signal

    def calc_indicator(self) -> None:
        """计算STC和HULL指标 (使用滤波后价格)"""
        # 使用卡尔曼滤波后的价格计算指标
        prices = self.kline_generator.producer.close_array
        if len(prices) < 30:
            return
        
        # 计算STC指标 (Schaff Trend Cycle)
        fast_period = 5    # 快速EMA周期
        slow_period = 20   # 慢速EMA周期
        cycle_period = 10  # STC周期
        signal_period = 3  # 信号线周期
        
        # 使用滤波后价格计算MACD线
        fast_ema = self.kline_generator.producer.ema(fast_period, array=True)
        slow_ema = self.kline_generator.producer.ema(slow_period, array=True)
        macd_line = fast_ema - slow_ema
        
        # 计算STC值
        lowest = np.minimum.accumulate(macd_line[-cycle_period:])
        highest = np.maximum.accumulate(macd_line[-cycle_period:])
        stoch = 100 * (macd_line[-1] - lowest[-1]) / (highest[-1] - lowest[-1] + 1e-9)
        
        # 平滑处理
        stc_value = self.kline_generator.producer.ema(signal_period, array=True, prices=stoch)[-1]
        stc_signal = self.kline_generator.producer.ema(signal_period, array=True, prices=stc_value)[-1]
        
        self.state_map.stc_value, self.state_map.stc_signal = np.round((stc_value, stc_signal), 2)

        # 计算HULL移动平均线 (Hull Moving Average)
        hull_period = 9  # HULL周期参数
        
        # 计算WMA(period/2)和WMA(period)
        half_period = hull_period // 2
        wma_half = self.kline_generator.producer.wma(half_period, array=True)
        wma_full = self.kline_generator.producer.wma(hull_period, array=True)
        
        # 计算原始HULL序列
        raw_hull = 2 * wma_half - wma_full
        
        # 计算最终HULL值 (WMA(sqrt(period)))
        sqrt_period = int(np.sqrt(hull_period))
        if self.kline_generator and hasattr(self.kline_generator, 'producer') and self.kline_generator.producer:
            hull_ma = self.kline_generator.producer.wma(sqrt_period, array=True, prices=raw_hull)
        else:
            # 简单的加权移动平均计算
            hull_ma = np.convolve(raw_hull, np.ones(sqrt_period)/sqrt_period, mode='valid')
        
        self.state_map.hull_prev, self.state_map.hull_value = np.round((hull_ma[-2], hull_ma[-1]), 2)
        
        # 李雅普诺夫稳定性分析
        state_vector = np.array([self.state_map.stc_value, self.state_map.hull_value])
        A_matrix = np.array([[0.9, 0.1], [0.05, 0.85]])  # 状态转移矩阵
        lyapunov_value = self.control_center.lyapunov_stability(state_vector, A_matrix)
        self.state_map.lyapunov_value = lyapunov_value

    def calc_signal(self, kline: KLineData):
        """计算交易信号"""
        self.calc_stc_hull_signal()
        self.long_price = self.short_price = kline.close
        
        if self.tick:
            # 使用滤波后价格作为交易参考
            self.long_price = max(self.state_map.filtered_price, self.tick.ask_price1)
            self.short_price = min(self.state_map.filtered_price, self.tick.bid_price1)
            
            if self.params_map.price_type == "D2":
                self.long_price = max(self.state_map.filtered_price, self.tick.ask_price2)
                self.short_price = min(self.state_map.filtered_price, self.tick.bid_price2)

    def on_order(self, order: OrderData):
        """订单回调(更新止盈止损状态)"""
        if order.status == "filled":
            if order.offset == "open":
                # 开仓时记录成本价
                self.state_map.position_cost = order.traded * (1.0003 if order.direction == "long" else 0.9997)
                self.state_map.max_profit = 0.0
                self.state_map.stop_triggered = False
                self.output(f"开仓成功: {order.direction} {order.volume}手 @ {order.traded:.2f}")
            else:
                # 平仓时重置状态
                self.state_map.position_cost = 0.0
                self.state_map.max_profit = 0.0
                self.state_map.stop_triggered = False
                self.output(f"平仓成功: {order.direction} {order.volume}手 @ {order.traded:.2f}")

    def exec_signal(self):
        """交易信号执行 (集成信号优先级和冲突解决)"""
        try:
            # 信号冷却检查
            current_time = time.time()
            if current_time - self.last_signal_time < self.signal_cooldown:
                return

            self.signal_price = 0
            position = self.get_position(self.params_map.instrument_id)

            # 收集所有信号
            signals = self._collect_all_signals()

            # 解决信号冲突
            if len(signals) > 1:
                primary_signal = self._signal_conflict_resolver.resolve_signal_conflict(signals)
            elif len(signals) == 1:
                primary_signal = signals[0]
            else:
                return  # 无信号

            # 更新信号时间
            self.last_signal_time = current_time

            # 记录信号历史
            self.signal_history.append({
                'timestamp': datetime.now(),
                'signal': primary_signal,
                'all_signals': signals
            })

            # 根据信号类型执行相应逻辑
            if primary_signal['type'] == 'emergency_stop':
                self._execute_emergency_stop()
                return
            elif primary_signal['type'] == 'risk_management':
                self._execute_risk_management_signal(primary_signal)
                return

            # 根据模糊风险等级和机器学习预测调整交易量
            risk_factor = self._calculate_risk_factor()
            adjusted_volume = self._calculate_adjusted_volume(risk_factor)

            if self.order_id is not None:
                self.cancel_order(self.order_id)

            # 如果风险等级为None，停止所有交易
            if self.state_map.fuzzy_risk == "RiskNone":
                self._execute_emergency_stop()
                return

            # 执行主要信号
            self._execute_primary_signal(primary_signal, adjusted_volume, position)

        except Exception as e:
            self.output(f"信号执行异常: {e}")

    def _collect_all_signals(self):
        """收集所有信号"""
        signals = []

        # 技术指标信号
        if self.buy_signal:
            signals.append({
                'type': 'technical_signal',
                'action': 'buy',
                'confidence': 0.7,
                'source': 'STC_HULL'
            })
        if self.sell_signal:
            signals.append({
                'type': 'technical_signal',
                'action': 'sell',
                'confidence': 0.7,
                'source': 'STC_HULL'
            })
        if self.short_signal:
            signals.append({
                'type': 'technical_signal',
                'action': 'short',
                'confidence': 0.7,
                'source': 'STC_HULL'
            })
        if self.cover_signal:
            signals.append({
                'type': 'technical_signal',
                'action': 'cover',
                'confidence': 0.7,
                'source': 'STC_HULL'
            })

        # 模糊决策信号
        if hasattr(self.state_map, 'fuzzy_action') and self.state_map.fuzzy_action:
            signals.append({
                'type': 'fuzzy_decision',
                'action': self.state_map.fuzzy_action,
                'confidence': getattr(self.state_map, 'fuzzy_confidence', 0.6),
                'source': 'FuzzySystem'
            })

        # ML预测信号
        if hasattr(self.state_map, 'ml_prediction') and self.state_map.ml_prediction:
            signals.append({
                'type': 'ml_prediction',
                'action': self.state_map.ml_prediction,
                'confidence': getattr(self.state_map, 'ml_confidence', 0.5),
                'source': 'MLPredictor'
            })

        # 风险管理信号
        if hasattr(self.state_map, 'risk_signal') and self.state_map.risk_signal:
            signals.append({
                'type': 'risk_management',
                'action': self.state_map.risk_signal,
                'confidence': 1.0,
                'source': 'RiskManager'
            })

        return signals

    def _calculate_risk_factor(self):
        """计算风险因子"""
        risk_factor = {
            "RiskNone": 0.0,
            "RiskLow": 0.3,
            "RiskMedium": 0.6,
            "RiskHigh": 0.9
        }.get(self.state_map.fuzzy_risk, 0.5)

        # 结合ML置信度调整风险因子
        if hasattr(self.state_map, 'ml_confidence'):
            ml_adjustment = self.state_map.ml_confidence * 0.5
            risk_factor = min(1.0, risk_factor * (1 + ml_adjustment))

        return risk_factor

    def _calculate_adjusted_volume(self, risk_factor):
        """计算调整后的交易量"""
        base_volume = self.params_map.order_volume
        if hasattr(self.state_map, 'optimal_position_size') and self.state_map.optimal_position_size > 0:
            optimal_volume = min(base_volume * 2, max(1, int(self.state_map.optimal_position_size)))
            base_volume = optimal_volume

        return max(1, int(base_volume * risk_factor))

    def _execute_emergency_stop(self):
        """执行紧急停止"""
        position = self.get_position(self.params_map.instrument_id)
        if position.volume != 0:
            self.execute_close_position(position, "emergency_stop")
        self.output("执行紧急停止信号")

    def _execute_risk_management_signal(self, signal):
        """执行风险管理信号"""
        if signal['action'] == 'close_all':
            position = self.get_position(self.params_map.instrument_id)
            if position.volume != 0:
                self.execute_close_position(position, "risk_management")
        self.output(f"执行风险管理信号: {signal['action']}")

    def _execute_primary_signal(self, signal, volume, position):
        """执行主要信号"""
        try:
            action = signal.get('action', '')

            # 平仓信号
            if action in ['sell', 'cover']:
                if position.net_position > 0 and action == 'sell':
                    self.signal_price = -self.short_price
                    if self.trading:
                        self.order_id = self.auto_close_position(
                            exchange=self.params_map.exchange,
                            instrument_id=self.params_map.instrument_id,
                            price=self.short_price,
                            volume=position.net_position,
                            order_direction="sell"
                        )
                elif position.net_position < 0 and action == 'cover':
                    self.signal_price = self.long_price
                    if self.trading:
                        self.order_id = self.auto_close_position(
                            exchange=self.params_map.exchange,
                            instrument_id=self.params_map.instrument_id,
                            price=self.long_price,
                            volume=abs(position.net_position),
                            order_direction="buy"
                        )

            # 开仓信号
            elif action in ['buy', 'short']:
                if action == 'short':
                    self.signal_price = -self.short_price
                    if self.trading:
                        self.order_id = self.send_order(
                            exchange=self.params_map.exchange,
                            instrument_id=self.params_map.instrument_id,
                            volume=volume,
                            price=self.short_price,
                            order_direction="sell"
                        )
                elif action == 'buy':
                    self.signal_price = self.long_price
                    if self.trading:
                        self.order_id = self.send_order(
                            exchange=self.params_map.exchange,
                            instrument_id=self.params_map.instrument_id,
                            volume=volume,
                            price=self.long_price,
                            order_direction="buy"
                        )

            # 记录信号执行
            self.output(f"执行信号: {signal['type']} - {action}, 置信度: {signal.get('confidence', 0):.2f}")

        except Exception as e:
            self.output(f"信号执行失败: {e}")

    def update_ml_predictions(self):
        """更新机器学习预测"""
        if len(self.price_history) < 20:
            return

        try:
            # 提取特征
            volume_data = self.volume_history if len(self.volume_history) > 0 else []
            features = self.ml_predictor.extract_features(
                self.price_history,
                volume_data
            )

            # 获取预测
            direction, confidence = self.ml_predictor.predict_direction(features)

            # 更新状态
            self.state_map.ml_direction = float(direction)
            self.state_map.ml_confidence = float(confidence)

            # 如果有足够的历史数据，进行模型训练
            if len(self.return_history) > 0:
                actual_return = self.return_history[-1]
                self.ml_predictor.add_training_data(features, actual_return)

        except Exception as e:
            self.output(f"ML预测更新失败: {str(e)}")

    def perform_risk_checks(self):
        """智能风险管理检查：多因子风险评分、情绪因子、黑天鹅检测"""
        if len(self.return_history) < 30:
            return
        try:
            # 1. 多因子风险评分
            var_95 = self.risk_manager.calculate_var(self.return_history, 0.95)
            es_95 = self.risk_manager.calculate_expected_shortfall(self.return_history, 0.95)
            # 情绪因子（如极端涨跌、成交量激增）
            sentiment_score = 0
            if len(self.price_history) > 20:
                price_change = (self.price_history[-1] - self.price_history[-20]) / self.price_history[-20]
                if price_change > 0.1:
                    sentiment_score -= 1  # 过热
                elif price_change < -0.1:
                    sentiment_score -= 1  # 恐慌
            if len(self.volume_history) > 20:
                vol_change = (self.volume_history[-1] - self.volume_history[-20]) / (self.volume_history[-20] + 1e-6)
                if vol_change > 1.5:
                    sentiment_score -= 1  # 放量异动
            # 黑天鹅检测（极端跳变）
            black_swan = False
            if len(self.price_history) > 5:
                recent_jumps = [abs(self.price_history[-i] - self.price_history[-i-1]) / (self.price_history[-i-1] + 1e-6) for i in range(1, 5)]
                if max(recent_jumps) > 0.08:
                    black_swan = True
            # 综合风险分
            risk_score = var_95 + es_95 + max(0, -sentiment_score) + (2 if black_swan else 0)
            self.state_map.current_var = var_95
            self.state_map.current_es = es_95
            # 压力测试
            if self.params_map and hasattr(self.params_map, 'instrument_id'):
                position = self.get_position(self.params_map.instrument_id)
                if position.net_position != 0 and self.risk_manager and self.state_map:
                    stress_results = self.risk_manager.stress_test(
                        position.net_position,
                        getattr(self.state_map, 'filtered_price', 0.0)
                    )
                worst_case_loss = min([result["pnl_pct"] for result in stress_results.values()])
                if worst_case_loss < -0.10:
                    self.output(f"风险警告: 压力测试显示潜在损失 {worst_case_loss:.2%}")
                    if abs(position.net_position) > 1:
                        reduce_volume = max(1, abs(position.net_position) // 2)
                        self.output(f"[风控] 触发极端风险事件，自动减仓 {reduce_volume} 手")
                        if self.params_map and self.state_map:
                            self.auto_close_position(
                                exchange=getattr(self.params_map, 'exchange', ''),
                                instrument_id=getattr(self.params_map, 'instrument_id', ''),
                                price=getattr(self.state_map, 'filtered_price', 0.0),
                                volume=reduce_volume
                            )
            # 智能风控建议
            if risk_score > 0.25:
                self.output(f"[AI风控] 综合风险评分={risk_score:.3f}，建议降低杠杆、减仓或暂停交易")
            if black_swan:
                self.output("[AI风控] 检测到黑天鹅事件，建议立即平仓或暂停交易")
            if var_95 > 0.08 or es_95 > 0.12:
                self.output(f"[风控] 检测到极端风险(VaR={var_95:.3f}, ES={es_95:.3f})，建议暂停交易或强制减仓")
        except Exception as e:
            self.output(f"风险检查失败: {str(e)}")

    async def adaptive_optimization_async(self):
        """异步自适应优化/风控模块：集成智能优化引擎，自动应用优化结果并动态调整主参数和风控参数"""
        base_interval = 60
        min_interval = 20
        max_interval = 180
        while True:
            try:
                # 1. 动态调整优化频率和风控阈值（保留原有逻辑）
                if len(self.return_history) > 30:
                    recent_returns = self.return_history[-30:]
                    volatility = float(np.std(recent_returns))
                    interval = int(max(min_interval, min(max_interval, base_interval * (0.05 / (volatility + 1e-6)))))
                    self.output(f"[自适应] 当前收益波动: {volatility:.4f}, 下次自适应间隔: {interval}s")
                    if volatility > 0.03:
                        self.params_map.quick_stop_loss = min(0.05, 0.02 + volatility)
                        self.params_map.trail_profit_stop = min(0.03, 0.01 + volatility / 2)
                    else:
                        self.params_map.quick_stop_loss = 0.02
                        self.params_map.trail_profit_stop = 0.01
                else:
                    interval = base_interval

                # 2. 智能优化引擎集成（核心，线程池异步）
                if hasattr(self, 'intelligent_optimizer'):
                    self.output("[智能优化] 启动多算法参数/模型优化")
                    loop = asyncio.get_event_loop()
                    # 贝叶斯优化
                    bayes_result = None
                    if BAYESIAN_AVAILABLE:
                        def bayes_obj(params):
                            learning_rate, momentum, feature_window, weight_decay = params
                            if hasattr(self.ml_predictor, 'learning_rate'):
                                self.ml_predictor.learning_rate = learning_rate
                            if hasattr(self.ml_predictor, 'momentum'):
                                self.ml_predictor.momentum = momentum
                            if hasattr(self.ml_predictor, 'price_history') and len(self.ml_predictor.price_history) > 50:
                                recent_returns = self.ml_predictor.price_history[-50:]
                                sharpe = np.mean(recent_returns) / (np.std(recent_returns) + 1e-6)
                                return -sharpe
                            return 0.0
                        bayes_result = await loop.run_in_executor(
                            None, lambda: self.intelligent_optimizer.bayesian_optimization(bayes_obj)
                        )
                        if bayes_result:
                            self.output(f"[贝叶斯优化] 最佳参数: {bayes_result}")
                            # 自动应用
                            if hasattr(self.ml_predictor, 'learning_rate'):
                                self.ml_predictor.learning_rate = bayes_result[0]
                            if hasattr(self.ml_predictor, 'momentum'):
                                self.ml_predictor.momentum = bayes_result[1]
                            # 可扩展更多参数
                            # 自动调整主参数N1、P1（如feature_window影响）
                            if hasattr(self.params_map, 'N1') and hasattr(self.params_map, 'P1'):
                                fw = int(bayes_result[2])
                                if 5 <= fw <= 20:
                                    self.params_map.N1 = fw
                                    self.params_map.P1 = max(5, min(20, fw + 2))
                                    self.output(f"[贝叶斯优化] 动态调整N1={self.params_map.N1}, P1={self.params_map.P1}")
                    # 遗传算法
                    genetic_result = None
                    if GENETIC_AVAILABLE:
                        genetic_result = await loop.run_in_executor(
                            None, lambda: self.intelligent_optimizer.genetic_optimization(population_size=30, generations=20)
                        )
                        if genetic_result:
                            self.output(f"[遗传算法] 最佳个体: {genetic_result}")
                            # 自动应用
                            try:
                                lr, fw, mo, wd = genetic_result
                                if hasattr(self.ml_predictor, 'learning_rate'):
                                    self.ml_predictor.learning_rate = lr
                                if hasattr(self.ml_predictor, 'momentum'):
                                    self.ml_predictor.momentum = mo
                                # 自动调整主参数N1、P1
                                if hasattr(self.params_map, 'N1') and hasattr(self.params_map, 'P1'):
                                    if 5 <= int(fw) <= 20:
                                        self.params_map.N1 = int(fw)
                                        self.params_map.P1 = max(5, min(20, int(fw) + 2))
                                        self.output(f"[遗传算法] 动态调整N1={self.params_map.N1}, P1={self.params_map.P1}")
                            except Exception as e:
                                self.output(f"[遗传算法参数应用失败] {e}")
                    # AutoML
                    automl_result = None
                    if AUTOML_AVAILABLE:
                        automl_result = await loop.run_in_executor(
                            None, self.intelligent_optimizer.automl_optimization
                        )
                        if automl_result and hasattr(self.intelligent_optimizer, 'best_model'):
                            self.output(f"[AutoML] 最佳模型: {automl_result}")
                            # 自动应用
                            self.ml_predictor.model = self.intelligent_optimizer.best_model
                    # Optuna
                    optuna_result = None
                    if OPTUNA_AVAILABLE:
                        optuna_result = await loop.run_in_executor(
                            None, self.intelligent_optimizer.optuna_optimization
                        )
                        if optuna_result:
                            self.output(f"[Optuna] 最佳参数: {optuna_result}")
                            # 自动应用
                            try:
                                if hasattr(self.ml_predictor, 'learning_rate'):
                                    self.ml_predictor.learning_rate = optuna_result.get('learning_rate', self.ml_predictor.learning_rate)
                                if hasattr(self.ml_predictor, 'momentum'):
                                    self.ml_predictor.momentum = optuna_result.get('momentum', self.ml_predictor.momentum)
                                # 自动调整主参数N1、P1
                                if hasattr(self.params_map, 'N1') and hasattr(self.params_map, 'P1'):
                                    fw = optuna_result.get('feature_window', self.params_map.N1)
                                    if 5 <= int(fw) <= 30:
                                        self.params_map.N1 = int(fw)
                                        self.params_map.P1 = max(5, min(30, int(fw) + 2))
                                        self.output(f"[Optuna] 动态调整N1={self.params_map.N1}, P1={self.params_map.P1}")
                            except Exception as e:
                                self.output(f"[Optuna参数应用失败] {e}")
                # 3. 智能风控自适应调整
                # 根据最大回撤、波动等动态调整order_volume、stability_margin等
                if (hasattr(self, 'max_drawdown') and self.max_drawdown and
                    float(self.max_drawdown) > 0.08 and self.params_map):
                    if hasattr(self.params_map, 'order_volume'):
                        old_vol = getattr(self.params_map, 'order_volume', 1)
                        self.params_map.order_volume = max(1, int(old_vol * 0.7))
                        self.output(f"[风控] 检测到较大回撤({self.max_drawdown:.2%})，自动降低开仓量: {old_vol} -> {self.params_map.order_volume}")
                # 根据波动率动态调整stability_margin
                if self.params_map and hasattr(self.params_map, 'stability_margin'):
                    if volatility > 0.04:
                        self.params_map.stability_margin = min(0.95, 0.7 + volatility)
                        self.output(f"[风控] 高波动期自动提升稳定裕度: {self.params_map.stability_margin:.2f}")
                    else:
                        self.params_map.stability_margin = 0.7
                # 4. 高级自定义风控规则
                if hasattr(self, 'advanced_risk_control'):
                    await self.advanced_risk_control()
                if hasattr(self, 'perform_risk_checks'):
                    self.perform_risk_checks()
                self.output("[异步自适应] 已完成一轮自适应优化/风控")
            except Exception as e:
                self.output(f"[异步自适应] 优化/风控异常: {e}")
            await asyncio.sleep(interval)

    async def advanced_risk_control(self):
        """高级自定义风控规则"""
        try:
            # 多因子风险评分
            risk_score = self.calculate_multi_factor_risk_score()
            
            # 动态止损止盈调整
            self.dynamic_stop_adjustment(risk_score)
            
            # 智能仓位管理
            self.intelligent_position_sizing(risk_score)
            
            # 市场情绪检测
            sentiment_score = self.detect_market_sentiment()
            
            # 极端事件检测
            self.extreme_event_detection()
            
            # 流动性风险检测
            self.liquidity_risk_detection()
            
        except Exception as e:
            self.output(f"[高级风控] 异常: {e}")
    
    def calculate_multi_factor_risk_score(self):
        """多因子风险评分"""
        risk_score = 0.0
        
        try:
            # 1. 波动率因子 (30%)
            if len(self.return_history) > 20:
                volatility = float(np.std(self.return_history[-20:]))
                vol_factor = min(1.0, volatility / 0.05)  # 标准化到5%波动率
                risk_score += vol_factor * 0.3

            # 2. 回撤因子 (25%)
            if hasattr(self, 'max_drawdown') and self.max_drawdown is not None:
                drawdown_factor = min(1.0, float(self.max_drawdown) / 0.15)  # 标准化到15%回撤
                risk_score += drawdown_factor * 0.25

            # 3. 夏普比率因子 (20%)
            if len(self.return_history) > 30:
                returns = np.array(self.return_history[-30:])
                sharpe = np.mean(returns) / (np.std(returns) + 1e-6)
                sharpe_factor = max(0, 1 - sharpe)  # 夏普比率越低风险越高
                risk_score += sharpe_factor * 0.2
            
            # 4. 相关性因子 (15%)
            if len(self.price_history) > 50:
                # 计算价格变化的自相关性
                price_changes = np.diff(self.price_history[-50:])
                autocorr = np.corrcoef(price_changes[:-1], price_changes[1:])[0, 1]
                corr_factor = abs(autocorr)  # 高自相关表示趋势性强，风险适中
                risk_score += corr_factor * 0.15
            
            # 5. 成交量因子 (10%)
            if len(self.volume_history) > 20:
                vol_ratio = float(np.mean(self.volume_history[-5:]) / (np.mean(self.volume_history[-20:]) + 1e-6))
                vol_factor = min(1.0, vol_ratio / 2.0)  # 成交量激增风险
                risk_score += vol_factor * 0.1
            
            self.output(f"[多因子风控] 综合风险评分: {risk_score:.3f}")
            return risk_score
            
        except Exception as e:
            self.output(f"[多因子风控] 计算异常: {e}")
            return 0.5
    
    def dynamic_stop_adjustment(self, risk_score):
        """动态止损止盈调整"""
        try:
            # 基础参数
            base_stop_loss = 0.02
            base_trail_start = 0.03
            base_trail_stop = 0.01
            
            # 根据风险评分动态调整
            if self.params_map:
                if risk_score > 0.7:  # 高风险
                    if hasattr(self.params_map, 'quick_stop_loss'):
                        self.params_map.quick_stop_loss = base_stop_loss * 0.8  # 更紧止损
                    if hasattr(self.params_map, 'trail_profit_start'):
                        self.params_map.trail_profit_start = base_trail_start * 0.8  # 更早止盈
                    if hasattr(self.params_map, 'trail_profit_stop'):
                        self.params_map.trail_profit_stop = base_trail_stop * 0.8  # 更紧回撤
                    self.output(f"[动态止损] 高风险模式: 止损={getattr(self.params_map, 'quick_stop_loss', 0):.3f}")
                elif risk_score < 0.3:  # 低风险
                    if hasattr(self.params_map, 'quick_stop_loss'):
                        self.params_map.quick_stop_loss = base_stop_loss * 1.2  # 更松止损
                    if hasattr(self.params_map, 'trail_profit_start'):
                        self.params_map.trail_profit_start = base_trail_start * 1.2  # 更晚止盈
                self.params_map.trail_profit_stop = base_trail_stop * 1.2  # 更松回撤
                self.output(f"[动态止损] 低风险模式: 止损={self.params_map.quick_stop_loss:.3f}, 止盈启动={self.params_map.trail_profit_start:.3f}")
            else:  # 中等风险
                self.params_map.quick_stop_loss = base_stop_loss
                self.params_map.trail_profit_start = base_trail_start
                self.params_map.trail_profit_stop = base_trail_stop
                
        except Exception as e:
            self.output(f"[动态止损] 调整异常: {e}")
    
    def intelligent_position_sizing(self, risk_score):
        """智能仓位管理"""
        try:
            # 基础仓位
            base_volume = 1
            
            # 根据风险评分调整仓位
            if risk_score > 0.8:  # 极高风险
                new_volume = max(1, int(base_volume * 0.5))
                self.output(f"[仓位管理] 极高风险，仓位降至: {new_volume}")
            elif risk_score > 0.6:  # 高风险
                new_volume = max(1, int(base_volume * 0.7))
                self.output(f"[仓位管理] 高风险，仓位降至: {new_volume}")
            elif risk_score < 0.2:  # 低风险
                new_volume = min(3, int(base_volume * 1.5))  # 最大3倍
                self.output(f"[仓位管理] 低风险，仓位提升至: {new_volume}")
            else:  # 中等风险
                new_volume = base_volume
            
            # 应用新仓位
            if hasattr(self.params_map, 'order_volume'):
                self.params_map.order_volume = new_volume
                
        except Exception as e:
            self.output(f"[仓位管理] 异常: {e}")
    
    def detect_market_sentiment(self):
        """市场情绪检测"""
        try:
            sentiment_score = 0.0
            
            # 1. 价格动量
            if len(self.price_history) > 20:
                price_momentum = (self.price_history[-1] - self.price_history[-20]) / self.price_history[-20]
                if abs(price_momentum) > 0.1:  # 10%以上变化
                    sentiment_score += 1 if price_momentum > 0 else -1
            
            # 2. 成交量异常
            if len(self.volume_history) > 20:
                vol_ratio = np.mean(self.volume_history[-5:]) / (np.mean(self.volume_history[-20:]) + 1e-6)
                if vol_ratio > 2.0:  # 成交量激增
                    sentiment_score += 0.5
            
            # 3. 波动率异常
            if len(self.return_history) > 10:
                recent_vol = np.std(self.return_history[-10:])
                if recent_vol > 0.05:  # 5%以上波动
                    sentiment_score += 0.5
            
            # 情绪判断
            if sentiment_score > 1.0:
                self.output(f"[情绪检测] 市场过热，情绪评分: {sentiment_score:.2f}")
                # 过热时降低仓位
                if hasattr(self.params_map, 'order_volume'):
                    current_volume = getattr(self.params_map, 'order_volume', 1)
                    self.params_map.order_volume = max(1, int(current_volume * 0.8))
            elif sentiment_score < -1.0:
                self.output(f"[情绪检测] 市场恐慌，情绪评分: {sentiment_score:.2f}")
                # 恐慌时暂停交易
                if hasattr(self.params_map, 'order_volume'):
                    self.params_map.order_volume = 0
            
            return sentiment_score
            
        except Exception as e:
            self.output(f"[情绪检测] 异常: {e}")
            return 0.0
    
    def extreme_event_detection(self):
        """极端事件检测"""
        try:
            # 1. 价格跳空检测
            if len(self.price_history) > 5:
                recent_jumps = []
                for i in range(1, 5):
                    jump = abs(self.price_history[-i] - self.price_history[-i-1]) / self.price_history[-i-1]
                    recent_jumps.append(jump)
                
                max_jump = max(recent_jumps)
                if max_jump > 0.08:  # 8%以上跳空
                    self.output(f"[极端事件] 检测到价格跳空: {max_jump:.2%}")
                    # 跳空后暂停交易
                    if hasattr(self.params_map, 'order_volume'):
                        self.params_map.order_volume = 0
            
            # 2. 连续亏损检测
            if len(self.return_history) > 10:
                recent_returns = self.return_history[-10:]
                consecutive_losses = sum(1 for r in recent_returns if r < 0)
                if consecutive_losses >= 8:  # 连续8次亏损
                    self.output(f"[极端事件] 检测到连续亏损: {consecutive_losses}次")
                    # 连续亏损时降低仓位
                    if hasattr(self.params_map, 'order_volume'):
                        self.params_map.order_volume = max(1, int(self.params_map.order_volume * 0.5))
            
            # 3. 异常波动检测
            if len(self.return_history) > 20:
                volatility = np.std(self.return_history[-20:])
                if volatility > 0.08:  # 8%以上波动
                    self.output(f"[极端事件] 检测到异常波动: {volatility:.2%}")
                    # 异常波动时收紧止损
                    self.params_map.quick_stop_loss = min(0.015, self.params_map.quick_stop_loss)
                    
        except Exception as e:
            self.output(f"[极端事件] 检测异常: {e}")
    
    def liquidity_risk_detection(self):
        """流动性风险检测"""
        try:
            # 1. 成交量萎缩检测
            if len(self.volume_history) > 20:
                recent_vol = np.mean(self.volume_history[-5:])
                avg_vol = np.mean(self.volume_history[-20:])
                vol_ratio = recent_vol / (avg_vol + 1e-6)
                
                if vol_ratio < 0.5:  # 成交量萎缩50%以上
                    self.output(f"[流动性] 检测到成交量萎缩: {vol_ratio:.2f}")
                    # 流动性不足时降低仓位
                    if hasattr(self.params_map, 'order_volume'):
                        self.params_map.order_volume = max(1, int(self.params_map.order_volume * 0.7))
            
            # 2. 价格连续性检测
            if len(self.price_history) > 10:
                price_changes = np.diff(self.price_history[-10:])
                zero_changes = sum(1 for pc in price_changes if abs(pc) < 1e-6)
                
                if zero_changes >= 5:  # 50%以上价格无变化
                    self.output(f"[流动性] 检测到价格停滞: {zero_changes}/10")
                    # 价格停滞时暂停交易
                    if hasattr(self.params_map, 'order_volume'):
                        self.params_map.order_volume = 0
                        
        except Exception as e:
            self.output(f"[流动性] 检测异常: {e}")

    def update_status_bar(self):
        """更新状态栏显示梯形模糊决策信息"""
        try:
            # 构建状态栏文本（用于日志输出）
            risk_symbol = {
                "RiskNone": "🚫",
                "RiskLow": "🟢", 
                "RiskMedium": "🟡",
                "RiskHigh": "🔴",
                "Medium": "🟡"  # 添加缺失的键
            }.get(self.state_map.fuzzy_risk, "🟡")  # 使用get方法提供默认值
            
            action_symbol = {
                "Stop": "⏹️",
                "Conservative": "🚸",
                "Normal": "➡️",
                "Aggressive": "⚡"
            }.get(self.state_map.fuzzy_action, "➡️")  # 使用get方法提供默认值
            
            # 获取ML和风险管理信息
            ml_info = ""
            if hasattr(self.state_map, 'ml_direction') and hasattr(self.state_map, 'ml_confidence'):
                ml_direction_symbol = "📈" if self.state_map.ml_direction > 0 else "📉" if self.state_map.ml_direction < 0 else "➡️"
                ml_info = f" | ML: {ml_direction_symbol} {self.state_map.ml_confidence:.0%}"

            risk_info = ""
            if hasattr(self.state_map, 'current_var'):
                risk_info = f" | VaR: {self.state_map.current_var:.3f}"

            optimal_size_info = ""
            if hasattr(self.state_map, 'optimal_position_size'):
                optimal_size_info = f" | 最优仓位: {self.state_map.optimal_position_size:.1f}"

            status_text = (
                f"STC: {self.state_map.stc_value:.2f}/{self.state_map.stc_signal:.2f} | "
                f"HULL: {self.state_map.hull_value:.2f} | "
                f"稳定: {self.state_map.system_stability:.2f} | "
                f"波动: {self.state_map.volatility_index:.4f} | "
                f"模糊决策: {risk_symbol}{action_symbol} {self.state_map.fuzzy_confidence:.0%}"
                f"{ml_info}{risk_info}{optimal_size_info}"
            )
            
            # 日志输出
            self.output(f"[状态栏] {status_text}")

            # 使用正确的状态栏更新方式（通过infini.update_state）
            # 这里会自动调用父类的update_status_bar方法，更新状态映射模型
            if hasattr(super(), 'update_status_bar'):
                super().update_status_bar()
            
        except Exception as e:
            if hasattr(self, 'output'):
                self.output(f"更新状态栏失败: {str(e)}")

    async def listen_market(self):
        """异步行情监听任务：从tick队列取数据，推送到指标队列"""
        while True:
            tick = await self.tick_queue.get()
            self.output(f"[异步监听] 收到tick: {getattr(tick, 'datetime', None)}")
            await self.indicator_queue.put(tick)

    async def calc_indicators_async(self):
        """异步技术指标计算任务：从indicator_queue取tick，计算指标，推送到决策队列"""
        while True:
            tick = await self.indicator_queue.get()
            # 这里直接调用现有的calc_indicator逻辑
            try:
                self.kline_generator.tick_to_kline(tick)
                self.calc_indicator()
                # 可将指标结果打包推送到决策队列
                indicator_result = {
                    'stc_value': self.state_map.stc_value,
                    'hull_value': self.state_map.hull_value,
                    'filtered_price': self.state_map.filtered_price,
                    'volatility_index': self.state_map.volatility_index,
                    'system_stability': self.state_map.system_stability,
                    'tick': tick
                }
                await self.decision_queue.put(indicator_result)
                self.output(f"[异步指标] 已推送指标结果到决策队列")
            except Exception as e:
                self.output(f"[异步指标] 计算异常: {e}")

    async def fuzzy_decision_async(self):
        """异步模糊决策模块：从决策队列取指标，执行模糊决策，推送到执行队列"""
        while True:
            indicator = await self.decision_queue.get()
            try:
                # 提取指标
                stc_value = indicator.get('stc_value', 0)
                hull_value = indicator.get('hull_value', 0)
                filtered_price = indicator.get('filtered_price', 0)
                volatility_index = indicator.get('volatility_index', 0)
                system_stability = indicator.get('system_stability', 1.0)
                tick = indicator.get('tick', None)
                # 计算当前盈利比例
                position_cost = getattr(self.state_map, 'position_cost', 0)
                profit = 0.0
                if position_cost > 0 and filtered_price > 0:
                    profit = (filtered_price - position_cost) / position_cost
                # 执行模糊决策
                decision = self.control_center.trapezoidal_fuzzy_decision(
                    system_stability,
                    volatility_index,
                    profit
                )
                # 推送到action_queue
                await self.action_queue.put({
                    'decision': decision,
                    'tick': tick,
                    'indicator': indicator
                })
                self.output(f"[异步决策] 已推送决策到执行队列: {decision}")
            except Exception as e:
                self.output(f"[异步决策] 计算异常: {e}")

    async def execute_action_async(self):
        """异步执行模块：从执行队列取决策，执行交易操作"""
        while True:
            action = await self.action_queue.get()
            try:
                decision = action.get('decision', None)
                tick = action.get('tick', None)
                indicator = action.get('indicator', {})
                if decision and tick:
                    # 根据决策执行交易
                    risk_level, action_level, confidence = decision
                    self.output(f"[异步执行] 执行决策: 风险={risk_level}, 行动={action_level}, 置信度={confidence}")
                    # 这里可以调用现有的交易逻辑
                    # 例如：self.exec_signal() 或直接下单逻辑
                    # 暂时只做日志记录，后续可扩展具体交易逻辑
            except Exception as e:
                self.output(f"[异步执行] 执行异常: {e}")



# 控制理论相关函数
def mason_formula(adjacency_matrix: np.ndarray) -> float:
    """梅森公式计算系统稳定性"""
    n = adjacency_matrix.shape[0]
    identity = np.eye(n)
    det = np.linalg.det(identity - adjacency_matrix)
    return det

def routh_stability(coeffs: np.ndarray) -> bool:
    """劳斯判据判断系统稳定性"""
    n = len(coeffs)
    if n < 2:
        return False
    
    routh_table = []
    routh_table.append(coeffs[0::2].tolist())
    second_row = coeffs[1::2].tolist()
    if n % 2 == 1:
        second_row.append(0)
    routh_table.append(second_row)
    
    for i in range(2, n):
        row = []
        for j in range(len(routh_table[i-1]) - 1):
            a = routh_table[i-2][j+1]
            b = routh_table[i-2][0]
            c = routh_table[i-1][j+1]
            d = routh_table[i-1][0]
            if d == 0:  # 避免除以零
                d = 1e-10
            element = (a * d - b * c) / d
            row.append(element)
        routh_table.append(row)
        
        # 检查第一列符号变化
        if row and row[0] * routh_table[i-1][0] < 0:
            return False
    
    return True

def nyquist_criterion(transfer_function: Callable, omega_range: tuple) -> bool:
    """奈奎斯特稳定判据"""
    omega = np.linspace(omega_range[0], omega_range[1], 1000)
    response = transfer_function(1j * omega)
    
    real_part = np.real(response)
    imag_part = np.imag(response)
    
    # 计算包围(-1,0)点的次数
    crossings = 0
    for i in range(len(real_part) - 1):
        if imag_part[i] < 0 and imag_part[i+1] >= 0 and real_part[i] < -1:
            crossings += 1
        elif imag_part[i] >= 0 and imag_part[i+1] < 0 and real_part[i] < -1:
            crossings -= 1
    
    return crossings == 0

class ControlCenter:
    """高级控制理论集成中心"""
    def __init__(self, strategy):
        self.strategy = strategy
        self.pid_history = []
        self.kalman_state = None
        self.lyapunov_history = []
        self.fuzzy_system = FuzzySystem()
        self.advanced_fuzzy_system = AdvancedFuzzySystem()
        self.decision_history = []
        
        # PID控制器参数
        self.kp = 0.5  # 比例系数
        self.ki = 0.1  # 积分系数
        self.kd = 0.2  # 微分系数
        
        # 卡尔曼滤波器参数
        self.kalman_Q = np.eye(2) * 0.01  # 过程噪声协方差
        self.kalman_R = np.eye(1) * 0.1   # 观测噪声协方差
        self.kalman_P = np.eye(2)         # 估计误差协方差
        
        # 模糊决策状态
        self.last_decision = ("RiskMedium", "Normal", 0.5)
        self.decision_counter = 0

    def trapezoidal_fuzzy_decision(self, stability, volatility, profit):
        """梯形模糊决策"""
        # 模糊化输入
        s_mem, v_mem, p_mem = self.advanced_fuzzy_system.fuzzify(stability, volatility, profit)

        # 执行推理
        decision = self.advanced_fuzzy_system.infer(s_mem, v_mem, p_mem)

        # 记录决策历史
        self.decision_history.append((stability, volatility, profit, decision))
        if len(self.decision_history) > 100:
            self.decision_history.pop(0)

        return decision

    def adaptive_trapezoidal_fuzzy_decision(self, stability, volatility, profit, market_data=None):
        """自适应梯形模糊决策，集成多源信息融合"""
        try:
            # 模糊化输入
            s_mem, v_mem, p_mem = self.advanced_fuzzy_system.fuzzify(stability, volatility, profit)

            # 执行自适应推理
            decision = self.advanced_fuzzy_system.adaptive_inference(
                s_mem, v_mem, p_mem, market_data
            )

            # 多源信息融合增强
            if market_data and 'price_history' in market_data:
                enhanced_decision = self._enhance_decision_with_fusion(
                    decision, market_data
                )
                decision = enhanced_decision

            # 记录决策历史
            input_data = (s_mem, v_mem, p_mem)
            self.decision_history.append((stability, volatility, profit, decision))

            # 添加到学习数据
            if hasattr(self.strategy, 'fuzzy_learning_data'):
                learning_record = {
                    'input': input_data,
                    'decision': decision,
                    'features': [stability, volatility, profit],
                    'timestamp': datetime.now()
                }
                self.strategy.fuzzy_learning_data.append(learning_record)

                # 保持学习数据在合理范围内
                if len(self.strategy.fuzzy_learning_data) > 1000:
                    self.strategy.fuzzy_learning_data.pop(0)

            if len(self.decision_history) > 100:
                self.decision_history.pop(0)

            return decision

        except Exception as e:
            print(f"自适应模糊决策失败: {e}")
            return self.trapezoidal_fuzzy_decision(stability, volatility, profit)

    def _enhance_decision_with_fusion(self, base_decision, market_data):
        """使用多源信息融合增强决策"""
        try:
            risk_level, action_level, confidence = base_decision

            # 提取技术信号
            technical_signals = []
            if 'price_history' in market_data and len(market_data['price_history']) > 20:
                prices = market_data['price_history']
                volumes = market_data.get('volume_history', [1000] * len(prices))

                technical_signals = self.advanced_fuzzy_system.info_fusion.extract_technical_features(
                    prices, volumes
                )

            # 提取情绪信号
            sentiment_signals = []
            if 'sentiment_score' in market_data:
                sentiment_signals = [market_data['sentiment_score']]
            elif 'price_history' in market_data and 'volume_history' in market_data:
                sentiment_signals = self.advanced_fuzzy_system.info_fusion.extract_market_sentiment(
                    market_data['price_history'], market_data['volume_history']
                )

            # 信息融合
            if technical_signals and sentiment_signals:
                # 确保信号长度一致
                min_length = min(len(technical_signals), len(sentiment_signals))
                if min_length > 0:
                    fused_signals = self.advanced_fuzzy_system.info_fusion.fuse_information(
                        technical_signals[-min_length:],
                        sentiment_signals[-min_length:]
                    )

                    if fused_signals:
                        fusion_score = fused_signals[-1]

                        # 根据融合得分调整决策
                        if fusion_score > 0.7:  # 强烈看涨信号
                            if risk_level in ["RiskLow", "RiskMedium"]:
                                risk_level = "RiskMedium" if risk_level == "RiskLow" else "RiskHigh"
                            if action_level in ["Conservative", "Normal"]:
                                action_level = "Normal" if action_level == "Conservative" else "Aggressive"
                            confidence = min(1.0, confidence + 0.1)

                        elif fusion_score < 0.3:  # 强烈看跌信号
                            if risk_level in ["RiskMedium", "RiskHigh"]:
                                risk_level = "RiskLow" if risk_level == "RiskMedium" else "RiskMedium"
                            if action_level in ["Normal", "Aggressive"]:
                                action_level = "Conservative" if action_level == "Normal" else "Normal"
                            confidence = min(1.0, confidence + 0.1)

            return (risk_level, action_level, confidence)

        except Exception as e:
            print(f"信息融合增强失败: {e}")
            return base_decision
    
    def adaptive_rule_update(self):
        """自适应规则更新"""
        if len(self.decision_history) < 20:
            return
        
        # 分析决策效果
        success_rate = self.calculate_decision_success()
        
        # 根据成功率调整规则权重
        if success_rate < 0.6:
            self.adjust_rule_weights(0.8)  # 降低权重
        elif success_rate > 0.8:
            self.adjust_rule_weights(1.2)  # 提高权重
    
    def calculate_decision_success(self):
        """计算决策成功率"""
        successes = 0
        for i in range(len(self.decision_history) - 1):
            _, _, _, decision = self.decision_history[i]
            next_profit = self.decision_history[i+1][2]
            
            # 根据决策和后续利润判断是否成功
            if decision[1] == "Aggressive" and next_profit > 0.02:
                successes += 1
            elif decision[1] == "Conservative" and next_profit > -0.01:
                successes += 1
            elif decision[1] == "Stop" and next_profit > -0.03:
                successes += 1
        
        return successes / (len(self.decision_history) - 1)
    
    def adjust_rule_weights(self, factor):
        """调整规则权重"""
        # 在实际实现中，这里会调整规则的激活阈值或权重
        # 简化实现：记录调整日志
        self.strategy.output(f"调整模糊规则权重: 因子={factor}")

    def update_pid(self, error: float) -> float:
        """多变量PID控制器"""
        if len(self.pid_history) < 2:
            self.pid_history.append(error)
            return error * self.kp
        
        integral = sum(self.pid_history)
        derivative = error - self.pid_history[-1]
        
        # 应用劳斯判据确保稳定性
        coeffs = np.array([self.kd, self.kp + self.kd, self.ki])
        if not routh_stability(coeffs):
            # 如果不稳定，调整参数
            self.kp *= 0.9
            self.ki *= 0.8
            self.kd *= 0.7
        
        output = (self.kp * error + 
                 self.ki * integral + 
                 self.kd * derivative)
        
        self.pid_history.append(error)
        if len(self.pid_history) > 100:
            self.pid_history.pop(0)
        
        return output
    
    def kalman_filter(self, measurement: float) -> float:
        """卡尔曼滤波状态评估"""
        if self.kalman_state is None:
            self.kalman_state = np.array([[measurement], [0.0]])  # [位置, 速度]
        
        # 预测步骤
        F = np.array([[1, 1], [0, 1]])  # 状态转移矩阵
        self.kalman_state = F @ self.kalman_state
        self.kalman_P = F @ self.kalman_P @ F.T + self.kalman_Q
        
        # 更新步骤
        H = np.array([[1, 0]])  # 观测矩阵
        y = measurement - H @ self.kalman_state
        S = H @ self.kalman_P @ H.T + self.kalman_R
        K = self.kalman_P @ H.T @ np.linalg.inv(S)
        
        self.kalman_state = self.kalman_state + K @ y
        self.kalman_P = (np.eye(2) - K @ H) @ self.kalman_P
        
        return float(self.kalman_state[0][0])
    
    def lyapunov_stability(self, state: np.ndarray, A: np.ndarray) -> float:
        """李雅普诺夫稳定性分析"""
        # 解李雅普诺夫方程: A'P + PA = -I
        n = A.shape[0]
        P = solve(A.T, -np.eye(n))
        P = (P + P.T) / 2  # 确保对称
        
        # 计算李雅普诺夫函数 V(x) = x'Px
        V = state.T @ P @ state
        self.lyapunov_history.append(float(V))
        
        if len(self.lyapunov_history) > 100:
            self.lyapunov_history.pop(0)
        
        # 检查稳定性 (V应为正定且递减)
        if len(self.lyapunov_history) > 5 and self.lyapunov_history[-1] > self.lyapunov_history[-5]:
            self.strategy.output("警告: 系统稳定性降低!")
        
        return float(V)
    
    def system_stability_check(self, transfer_function: Callable) -> bool:
        """综合稳定性检查"""
        # 梅森公式判据
        adj_matrix = np.array([[0.2, 0.3, 0.1],
                               [0.1, 0.4, 0.2],
                               [0.3, 0.1, 0.2]])
        mason_stable = abs(mason_formula(adj_matrix)) > 0.5
        
        # 奈奎斯特判据
        nyquist_stable = nyquist_criterion(transfer_function, (0.1, 100))
        
        return mason_stable and nyquist_stable
    
    def fuzzy_decision(self, stability, volatility, profit):
        """执行模糊决策并更新策略参数"""
        self.decision_counter += 1
        
        # 每分钟更新一次决策
        if self.decision_counter >= 60:
            self.last_decision = self.fuzzy_system.make_decision(stability, volatility, profit)
            self.decision_counter = 0
            self.strategy.output(f"模糊决策: 风险={self.last_decision[0]}, 行动={self.last_decision[1]}, 置信度={self.last_decision[2]:.2f}")
        
        return self.last_decision
    
    def apply_fuzzy_decision(self, decision):
        """应用模糊决策到策略参数"""
        risk_level, action_level, _ = decision
        
        # 根据风险等级调整交易量
        risk_factor = {
            "RiskNone": 0.0,
            "RiskLow": 0.3,
            "RiskMedium": 0.6,
            "RiskHigh": 0.9
        }[risk_level]
        
        # 根据行动级别调整策略行为
        if action_level == "Stop":
            self.strategy.params_map.order_volume = 0
        elif action_level == "Conservative":
            self.strategy.params_map.trail_profit_start = 0.02
            self.strategy.params_map.quick_stop_loss = 0.015
        elif action_level == "Normal":
            self.strategy.params_map.trail_profit_start = 0.03
            self.strategy.params_map.quick_stop_loss = 0.02
        elif action_level == "Aggressive":
            self.strategy.params_map.trail_profit_start = 0.04
            self.strategy.params_map.quick_stop_loss = 0.025
        
        return risk_factor

# 智能优化引擎
class IntelligentOptimizer:
    """集成贝叶斯优化、遗传算法和AutoML的智能优化引擎"""
    
    def __init__(self, strategy):
        self.strategy = strategy
        self.optimization_history = []
        self.best_params = {}
        self.optimization_counter = 0
        
        # 贝叶斯优化器
        if BAYESIAN_AVAILABLE:
            try:
                self.bayesian_optimizer = Optimizer(
                    dimensions=[
                        Real(0.01, 0.1, name='learning_rate'),
                        Real(0.1, 0.9, name='momentum'),
                        Integer(5, 20, name='feature_window'),
                        Real(0.001, 0.01, name='weight_decay')
                    ],
                    base_estimator='GP',
                    acq_func='EI',
                    n_initial_points=10
                )
            except Exception as e:
                print(f"贝叶斯优化器初始化失败: {e}")
                self.bayesian_optimizer = None
        else:
            self.bayesian_optimizer = None
        
        # 遗传算法设置
        if GENETIC_AVAILABLE:
            self.setup_genetic_algorithm()
        
        # AutoML模型池
        if AUTOML_AVAILABLE:
            try:
                from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
                from sklearn.linear_model import LinearRegression, Ridge
                from sklearn.svm import SVR
                from sklearn.preprocessing import StandardScaler

                self.model_pool = {
                    'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                    'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
                    'linear_regression': LinearRegression(),
                    'ridge_regression': Ridge(alpha=1.0),
                    'svr': SVR(kernel='rbf', C=1.0, gamma='scale')
                }
                self.best_model = None
                self.model_performance = {}
                self.scaler = StandardScaler()
            except Exception as e:
                print(f"AutoML模型池初始化失败: {e}")
                self.model_pool = {}
                self.best_model = None
                self.model_performance = {}
                self.scaler = None
        else:
            self.model_pool = {}
            self.best_model = None
            self.model_performance = {}
            self.scaler = None
        
        # Optuna优化器
        if OPTUNA_AVAILABLE:
            self.optuna_study = None
    
    def setup_genetic_algorithm(self):
        """设置遗传算法"""
        if not GENETIC_AVAILABLE:
            return

        try:
            from deap import base, creator, tools
            import random

            # 创建适应度类
            if not hasattr(creator, "FitnessMax"):
                creator.create("FitnessMax", base.Fitness, weights=(1.0,))
            if not hasattr(creator, "Individual"):
                creator.create("Individual", list, fitness=creator.FitnessMax)
        
            # 创建工具箱
            self.toolbox = base.Toolbox()

            # 基因生成函数
            self.toolbox.register("attr_float", random.uniform, 0.0, 1.0)
            self.toolbox.register("attr_int", random.randint, 1, 50)

            # 个体和种群生成
            self.toolbox.register("individual", tools.initCycle, creator.Individual,
                                 (self.toolbox.attr_float, self.toolbox.attr_int,
                                  self.toolbox.attr_float, self.toolbox.attr_float), n=1)
            self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)

            # 遗传操作
            self.toolbox.register("evaluate", self.evaluate_genetic_fitness)
            self.toolbox.register("mate", tools.cxTwoPoint)
            self.toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=0.1, indpb=0.2)
            self.toolbox.register("select", tools.selTournament, tournsize=3)

        except Exception as e:
            print(f"遗传算法设置失败: {e}")
            self.toolbox = None
    
    def evaluate_genetic_fitness(self, individual):
        """评估遗传算法适应度"""
        try:
            # 解包个体参数
            learning_rate, feature_window, momentum, weight_decay = individual
            
            # 应用参数到ML预测器
            self.strategy.ml_predictor.learning_rate = learning_rate
            self.strategy.ml_predictor.momentum = momentum
            
            # 计算适应度（基于历史表现）
            if len(self.strategy.return_history) > 50:
                recent_returns = self.strategy.return_history[-50:]
                sharpe_ratio = np.mean(recent_returns) / (np.std(recent_returns) + 1e-6)
                return (sharpe_ratio,)
            else:
                return (0.0,)
        except Exception as e:
            self.strategy.output(f"[遗传算法] 适应度评估失败: {e}")
            return (0.0,)
    
    def bayesian_optimization(self, objective_func):
        """贝叶斯优化"""
        if not BAYESIAN_AVAILABLE:
            return None
        
        try:
            # 运行贝叶斯优化
            result = gp_minimize(
                objective_func,
                dimensions=[
                    Real(0.01, 0.1, name='learning_rate'),
                    Real(0.1, 0.9, name='momentum'),
                    Integer(5, 20, name='feature_window'),
                    Real(0.001, 0.01, name='weight_decay')
                ],
                n_calls=20,
                random_state=42
            )
            
            return result.x
        except Exception as e:
            self.strategy.output(f"[贝叶斯优化] 失败: {e}")
            return None
    
    def genetic_optimization(self, population_size=30, generations=20):
        """遗传算法优化"""
        if not GENETIC_AVAILABLE:
            return None
        
        try:
            # 创建初始种群
            population = self.toolbox.population(n=population_size)
            
            # 评估初始种群
            fitnesses = list(map(self.toolbox.evaluate, population))
            for ind, fit in zip(population, fitnesses):
                ind.fitness.values = fit
            
            # 进化循环
            for gen in range(generations):
                # 选择下一代个体
                offspring = map(self.toolbox.clone, self.toolbox.select(population, len(population)))
                offspring = list(offspring)
                
                # 交叉和变异
                for child1, child2 in zip(offspring[::2], offspring[1::2]):
                    if random.random() < 0.7:
                        self.toolbox.mate(child1, child2)
                        del child1.fitness.values
                        del child2.fitness.values
                
                for mutant in offspring:
                    if random.random() < 0.2:
                        self.toolbox.mutate(mutant)
                        del mutant.fitness.values
                
                # 评估新个体
                invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
                fitnesses = map(self.toolbox.evaluate, invalid_ind)
                for ind, fit in zip(invalid_ind, fitnesses):
                    ind.fitness.values = fit
                
                # 替换种群
                population[:] = offspring
            
            # 返回最佳个体
            best_individual = tools.selBest(population, 1)[0]
            return best_individual
            
        except Exception as e:
            self.strategy.output(f"[遗传算法] 优化失败: {e}")
            return None
    
    def automl_optimization(self):
        """AutoML模型选择和优化"""
        if not AUTOML_AVAILABLE:
            return None
        
        try:
            # 准备训练数据
            if len(self.strategy.ml_predictor.feature_history) < 50:
                return None
            
            X = np.array(self.strategy.ml_predictor.feature_history[-100:])
            y = np.array(self.strategy.ml_predictor.price_history[-100:])
            
            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)
            
            # 评估所有模型
            for name, model in self.model_pool.items():
                try:
                    # 交叉验证
                    scores = cross_val_score(model, X_scaled, y, cv=3, scoring='neg_mean_squared_error')
                    mse = -np.mean(scores)
                    self.model_performance[name] = mse
                except Exception as e:
                    self.strategy.output(f"[AutoML] 模型{name}评估失败: {e}")
                    self.model_performance[name] = float('inf')
            
            # 选择最佳模型
            best_model_name = min(self.model_performance, key=self.model_performance.get)
            self.best_model = self.model_pool[best_model_name]
            
            # 训练最佳模型
            self.best_model.fit(X_scaled, y)
            
            return best_model_name
            
        except Exception as e:
            self.strategy.output(f"[AutoML] 优化失败: {e}")
            return None
    
    def optuna_optimization(self):
        """Optuna超参数优化"""
        if not OPTUNA_AVAILABLE:
            return None
        
        try:
            def objective(trial):
                # 定义超参数空间
                learning_rate = trial.suggest_float('learning_rate', 0.01, 0.1)
                momentum = trial.suggest_float('momentum', 0.1, 0.9)
                feature_window = trial.suggest_int('feature_window', 5, 20)
                weight_decay = trial.suggest_float('weight_decay', 0.001, 0.01)
                
                # 应用参数
                self.strategy.ml_predictor.learning_rate = learning_rate
                self.strategy.ml_predictor.momentum = momentum
                
                # 计算目标函数（负的夏普比率）
                if len(self.strategy.return_history) > 30:
                    recent_returns = self.strategy.return_history[-30:]
                    sharpe = np.mean(recent_returns) / (np.std(recent_returns) + 1e-6)
                    return -sharpe
                else:
                    return 0.0
            
            # 创建研究
            study = optuna.create_study(direction='minimize')
            study.optimize(objective, n_trials=20)
            
            return study.best_params
            
        except Exception as e:
            self.strategy.output(f"[Optuna] 优化失败: {e}")
            return None

# 异步模糊处理扩展方法（添加到Strategy3类）
def _setup_async_processing(self):
    """设置异步模糊处理"""
    if not self.async_processor:
        return

    # 启动异步处理器
    try:
        # 在后台线程中启动异步事件循环
        def start_async_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.async_processor.start())
            loop.run_forever()

        self.async_thread = threading.Thread(target=start_async_loop, daemon=True)
        self.async_thread.start()

        self.output("异步模糊处理器已启动")

    except Exception as e:
        self.output(f"异步处理器启动失败: {e}")

async def async_fuzzy_decision(self, stability, volatility, profit, market_data=None):
    """异步模糊决策"""
    if not self.async_processor:
        # 回退到同步决策
        return self.control_center.adaptive_trapezoidal_fuzzy_decision(
            stability, volatility, profit, market_data
        )

    try:
        # 准备输入数据
        s_mem, v_mem, p_mem = self.control_center.advanced_fuzzy_system.fuzzify(
            stability, volatility, profit
        )
        input_data = (s_mem, v_mem, p_mem)

        # 提交异步决策请求
        result = await self.async_processor.submit_decision_request(
            self.control_center.advanced_fuzzy_system,
            input_data,
            priority=1
        )

        return result['decision']

    except Exception as e:
        self.output(f"异步模糊决策失败: {e}")
        # 回退到同步决策
        return self.control_center.adaptive_trapezoidal_fuzzy_decision(
            stability, volatility, profit, market_data
        )

def get_async_performance_stats(self):
    """获取异步处理性能统计"""
    if not self.async_processor:
        return None

    try:
        stats = self.async_processor.get_performance_stats()
        return stats
    except Exception as e:
        self.output(f"获取异步性能统计失败: {e}")
        return None

# 信号过滤相关方法
def _validate_signal_quality(self, processed_data: Dict[str, Any]) -> bool:
    """验证信号质量"""
    try:
        # 1. 数据完整性检查
        required_fields = ['price', 'volatility', 'stability']
        if not all(field in processed_data for field in required_fields):
            return False

        # 2. 数据合理性检查
        price = processed_data.get('price', 0)
        volatility = processed_data.get('volatility', 0)

        if price <= 0 or volatility < 0 or volatility > 1.0:
            return False

        # 3. 市场数据新鲜度检查
        if hasattr(self, 'last_data_timestamp'):
            current_time = time.time()
            if current_time - self.last_data_timestamp > 300:  # 5分钟内的数据
                return False

        self.last_data_timestamp = time.time()

        # 4. 价格异常检查
        if hasattr(self, 'price_history') and len(self.price_history) > 5:
            recent_avg = np.mean(self.price_history[-5:])
            if abs(price - recent_avg) / recent_avg > 0.1:  # 价格变化超过10%
                return False

        return True
    except Exception:
        return False

def _filter_signals(self, decisions: Dict[str, Dict[str, Any]],
                   processed_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """过滤低质量信号"""
    filtered_decisions = {}

    for system_name, decision in decisions.items():
        if self._is_signal_valid(decision, system_name, processed_data):
            filtered_decisions[system_name] = decision
        else:
            # 用hold信号替换无效信号
            filtered_decisions[system_name] = {
                'action': 'hold',
                'confidence': 0.1,
                'filtered': True
            }

    return filtered_decisions

def _is_signal_valid(self, decision: Dict[str, Any], system_name: str,
                    processed_data: Dict[str, Any]) -> bool:
    """检查单个信号是否有效"""
    try:
        # 1. 置信度阈值检查
        confidence = decision.get('confidence', 0.0)
        min_confidence_thresholds = {
            'fuzzy': 0.6,
            'ml': 0.55,
            'neural': 0.55,
            'hybrid': 0.65
        }

        min_confidence = min_confidence_thresholds.get(system_name, 0.6)
        if confidence < min_confidence:
            return False

        # 2. 动作一致性检查
        action = decision.get('action', 'hold')
        if action not in ['buy', 'sell', 'hold']:
            return False

        # 3. 系统特定验证
        if system_name == 'fuzzy':
            return self._validate_fuzzy_signal(decision, processed_data)
        elif system_name == 'ml':
            return self._validate_ml_signal(decision, processed_data)
        elif system_name == 'neural':
            return self._validate_neural_signal(decision, processed_data)
        elif system_name == 'hybrid':
            return self._validate_hybrid_signal(decision, processed_data)

        return True
    except Exception:
        return False

def _check_signal_strength(self, decision: Dict[str, Any],
                          processed_data: Dict[str, Any]) -> bool:
    """检查信号强度"""
    try:
        confidence = decision.get('confidence', 0.0)
        action = decision.get('action', 'hold')

        # hold信号总是可以接受的
        if action == 'hold':
            return True

        # 交易信号需要足够的强度
        min_strength = 0.65  # 提高最低强度要求

        # 根据市场状态调整强度要求
        market_volatility = processed_data.get('volatility', 0.02)
        if market_volatility > 0.03:  # 高波动市场
            min_strength = 0.75
        elif market_volatility < 0.01:  # 低波动市场
            min_strength = 0.6

        return confidence >= min_strength
    except Exception:
        return False

def _check_market_adaptability(self, decision: Dict[str, Any],
                              processed_data: Dict[str, Any]) -> bool:
    """检查市场适应性"""
    try:
        action = decision.get('action', 'hold')

        if action == 'hold':
            return True

        # 检查市场状态是否适合交易
        market_volatility = processed_data.get('volatility', 0.02)

        # 极端波动市场暂停交易
        if market_volatility > 0.05:  # 超过5%波动率
            return False

        # 检查是否在交易时间窗口内
        current_hour = datetime.now().hour
        if current_hour < 9 or current_hour > 15:  # 非交易时间
            return False

        return True
    except Exception:
        return True

def _final_signal_confirmation(self, decision: Dict[str, Any],
                              processed_data: Dict[str, Any]) -> Dict[str, Any]:
    """最终信号确认"""
    try:
        confirmed_decision = decision.copy()

        # 最后一次置信度检查
        confidence = decision.get('confidence', 0.0)
        action = decision.get('action', 'hold')

        # 如果置信度不足，强制转为hold
        if action != 'hold' and confidence < 0.7:
            confirmed_decision['action'] = 'hold'
            confirmed_decision['position_size'] = 0.0
            confirmed_decision['confirmation_reason'] = '最终置信度检查未通过'

        # 添加确认标记
        confirmed_decision['confirmed'] = True
        confirmed_decision['confirmation_time'] = datetime.now().isoformat()

        return confirmed_decision
    except Exception:
        return decision

def _get_hold_decision(self, reason: str = "默认hold") -> Dict[str, Any]:
    """获取hold决策"""
    return {
        'action': 'hold',
        'confidence': 0.5,
        'position_size': 0.0,
        'reason': reason,
        'timestamp': datetime.now().isoformat()
    }

# 注释掉方法绑定以避免循环引用问题
# Strategy3._setup_async_processing = _setup_async_processing
# Strategy3.async_fuzzy_decision = async_fuzzy_decision
# Strategy3.get_async_performance_stats = get_async_performance_stats
# Strategy3._validate_signal_quality = _validate_signal_quality
# Strategy3._filter_signals = _filter_signals
# Strategy3._is_signal_valid = _is_signal_valid
# Strategy3._check_signal_strength = _check_signal_strength
# Strategy3._check_market_adaptability = _check_market_adaptability
# Strategy3._final_signal_confirmation = _final_signal_confirmation
# Strategy3._get_hold_decision = _get_hold_decision

# 信号验证具体实现
def _validate_fuzzy_signal(self, decision: Dict[str, Any],
                          processed_data: Dict[str, Any]) -> bool:
    """验证模糊信号"""
    try:
        # 检查模糊推理的合理性
        risk_level = decision.get('risk_level', 'RiskMedium')
        action = decision.get('action', 'hold')

        # 高风险时不应该有激进买入信号
        if risk_level in ['RiskHigh', 'RiskNone'] and action == 'buy':
            return False

        # 检查与市场波动率的一致性
        market_volatility = processed_data.get('volatility', 0.02)
        if market_volatility > 0.04 and action in ['buy', 'sell']:
            # 高波动市场需要更高的置信度
            return decision.get('confidence', 0.0) > 0.7

        return True
    except Exception:
        return False

def _validate_ml_signal(self, decision: Dict[str, Any],
                       processed_data: Dict[str, Any]) -> bool:
    """验证ML信号"""
    try:
        # ML信号需要有预测值
        prediction = decision.get('prediction', None)
        if prediction is None:
            return False

        # 预测值应该在合理范围内
        if not (-1.0 <= prediction <= 1.0):
            return False

        return True
    except Exception:
        return False

def _validate_neural_signal(self, decision: Dict[str, Any],
                           processed_data: Dict[str, Any]) -> bool:
    """验证神经网络信号"""
    try:
        # 神经网络信号需要有输出值
        output = decision.get('output', None)
        if output is None:
            return False

        # 检查输出的稳定性
        confidence = decision.get('confidence', 0.0)
        if confidence < 0.5:
            return False

        return True
    except Exception:
        return False

def _validate_hybrid_signal(self, decision: Dict[str, Any],
                           processed_data: Dict[str, Any]) -> bool:
    """验证混合信号"""
    try:
        # 混合信号应该有更高的质量要求
        confidence = decision.get('confidence', 0.0)
        if confidence < 0.65:
            return False

        # 检查组件信号的一致性
        components = decision.get('components', {})
        if components:
            actions = [comp.get('action', 'hold') for comp in components.values()]
            # 如果组件信号分歧太大，降低信任度
            unique_actions = set(actions)
            if len(unique_actions) > 2:  # 超过2种不同动作
                return False

        return True
    except Exception:
        return False

# 注释掉函数绑定以避免循环引用问题
# Strategy3._validate_fuzzy_signal = _validate_fuzzy_signal
# Strategy3._validate_ml_signal = _validate_ml_signal
# Strategy3._validate_neural_signal = _validate_neural_signal
# Strategy3._validate_hybrid_signal = _validate_hybrid_signal
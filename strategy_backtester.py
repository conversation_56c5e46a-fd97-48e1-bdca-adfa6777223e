"""
策略回测框架
用于测试和比较不同的交易策略性能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import matplotlib.pyplot as plt
import seaborn as sns
from abc import ABC, abstractmethod


@dataclass
class Trade:
    """交易记录"""
    entry_time: datetime
    exit_time: Optional[datetime] = None
    entry_price: float = 0.0
    exit_price: float = 0.0
    quantity: int = 1
    direction: str = "long"  # long or short
    entry_reason: str = ""
    exit_reason: str = ""
    pnl: float = 0.0
    commission: float = 0.0
    slippage: float = 0.0
    
    @property
    def is_open(self) -> bool:
        return self.exit_time is None
    
    @property
    def duration(self) -> Optional[timedelta]:
        if self.exit_time:
            return self.exit_time - self.entry_time
        return None
    
    @property
    def return_pct(self) -> float:
        if self.exit_price > 0:
            if self.direction == "long":
                return (self.exit_price - self.entry_price) / self.entry_price
            else:
                return (self.entry_price - self.exit_price) / self.entry_price
        return 0.0


@dataclass
class BacktestResult:
    """回测结果"""
    trades: List[Trade] = field(default_factory=list)
    equity_curve: List[float] = field(default_factory=list)
    timestamps: List[datetime] = field(default_factory=list)
    
    # 性能指标
    total_return: float = 0.0
    annual_return: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_trade_return: float = 0.0
    max_consecutive_losses: int = 0
    
    # 风险指标
    var_95: float = 0.0  # 95% VaR
    calmar_ratio: float = 0.0
    sortino_ratio: float = 0.0
    
    def calculate_metrics(self):
        """计算性能指标"""
        if not self.trades:
            return
            
        # 基础统计
        closed_trades = [t for t in self.trades if not t.is_open]
        if not closed_trades:
            return
            
        returns = [t.return_pct for t in closed_trades]
        pnls = [t.pnl for t in closed_trades]
        
        # 收益率指标
        self.total_return = sum(returns)
        self.avg_trade_return = np.mean(returns)
        
        # 胜率
        winning_trades = [t for t in closed_trades if t.pnl > 0]
        self.win_rate = len(winning_trades) / len(closed_trades) if closed_trades else 0
        
        # 盈亏比
        gross_profit = sum([t.pnl for t in winning_trades])
        gross_loss = abs(sum([t.pnl for t in closed_trades if t.pnl < 0]))
        self.profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # 最大连续亏损
        consecutive_losses = 0
        max_consecutive = 0
        for trade in closed_trades:
            if trade.pnl < 0:
                consecutive_losses += 1
                max_consecutive = max(max_consecutive, consecutive_losses)
            else:
                consecutive_losses = 0
        self.max_consecutive_losses = max_consecutive
        
        # 计算资金曲线相关指标
        if len(self.equity_curve) > 1:
            equity_returns = np.diff(self.equity_curve) / self.equity_curve[:-1]
            
            # 年化收益率
            if len(self.timestamps) > 1:
                days = (self.timestamps[-1] - self.timestamps[0]).days
                if days > 0:
                    self.annual_return = (self.equity_curve[-1] / self.equity_curve[0]) ** (365.25 / days) - 1
            
            # 夏普比率
            if len(equity_returns) > 1:
                self.sharpe_ratio = np.mean(equity_returns) / np.std(equity_returns) * np.sqrt(252) if np.std(equity_returns) > 0 else 0
            
            # 最大回撤
            peak = np.maximum.accumulate(self.equity_curve)
            drawdown = (self.equity_curve - peak) / peak
            self.max_drawdown = abs(np.min(drawdown))
            
            # VaR
            self.var_95 = np.percentile(equity_returns, 5) if len(equity_returns) > 0 else 0
            
            # Calmar比率
            self.calmar_ratio = self.annual_return / self.max_drawdown if self.max_drawdown > 0 else 0
            
            # Sortino比率
            negative_returns = equity_returns[equity_returns < 0]
            downside_std = np.std(negative_returns) if len(negative_returns) > 0 else 0
            self.sortino_ratio = np.mean(equity_returns) / downside_std * np.sqrt(252) if downside_std > 0 else 0


class BaseBacktestStrategy(ABC):
    """回测策略基类"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}
        self.trades = []
        
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def calculate_position_size(self, signal: Dict, current_price: float) -> int:
        """计算仓位大小"""
        pass
    
    def execute_trade(self, signal: Dict, current_price: float, timestamp: datetime) -> Optional[Trade]:
        """执行交易"""
        if signal['action'] == 'buy':
            quantity = self.calculate_position_size(signal, current_price)
            if quantity > 0:
                trade = Trade(
                    entry_time=timestamp,
                    entry_price=current_price,
                    quantity=quantity,
                    direction="long",
                    entry_reason=signal.get('reason', '')
                )
                self.trades.append(trade)
                return trade
                
        elif signal['action'] == 'sell':
            # 平仓逻辑
            open_trades = [t for t in self.trades if t.is_open]
            if open_trades:
                trade = open_trades[-1]  # 平最新的仓位
                trade.exit_time = timestamp
                trade.exit_price = current_price
                trade.exit_reason = signal.get('reason', '')
                trade.pnl = (trade.exit_price - trade.entry_price) * trade.quantity
                return trade
        
        return None


class StrategyBacktester:
    """策略回测器"""
    
    def __init__(self, commission: float = 0.0002, slippage: float = 0.0001):
        self.commission = commission
        self.slippage = slippage
        
    def run_backtest(self, strategy: BaseBacktestStrategy, data: pd.DataFrame) -> BacktestResult:
        """运行回测"""
        result = BacktestResult()
        
        # 生成信号
        signals_df = strategy.generate_signals(data)
        
        # 初始化资金曲线
        equity = strategy.initial_capital
        result.equity_curve.append(equity)
        result.timestamps.append(data.index[0])
        
        # 逐行处理数据
        for i, (timestamp, row) in enumerate(data.iterrows()):
            if i < len(signals_df):
                signal_row = signals_df.iloc[i]
                
                # 检查是否有信号
                if not pd.isna(signal_row.get('action')):
                    signal = {
                        'action': signal_row['action'],
                        'reason': signal_row.get('reason', ''),
                        'confidence': signal_row.get('confidence', 1.0)
                    }
                    
                    # 执行交易
                    trade = strategy.execute_trade(signal, row['close'], timestamp)
                    if trade:
                        # 计算手续费和滑点
                        trade.commission = trade.entry_price * trade.quantity * self.commission
                        trade.slippage = trade.entry_price * trade.quantity * self.slippage
                        
                        if not trade.is_open:
                            # 更新资金
                            equity += trade.pnl - trade.commission - trade.slippage
            
            # 更新资金曲线
            result.equity_curve.append(equity)
            result.timestamps.append(timestamp)
        
        # 保存交易记录
        result.trades = strategy.trades.copy()
        
        # 计算性能指标
        result.calculate_metrics()
        
        return result
    
    def compare_strategies(self, strategies: Dict[str, BaseBacktestStrategy], 
                          data: pd.DataFrame) -> Dict[str, BacktestResult]:
        """比较多个策略"""
        results = {}
        for name, strategy in strategies.items():
            print(f"回测策略: {name}")
            results[name] = self.run_backtest(strategy, data)
        return results
    
    def plot_results(self, results: Dict[str, BacktestResult], save_path: Optional[str] = None):
        """绘制回测结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 资金曲线
        ax1 = axes[0, 0]
        for name, result in results.items():
            ax1.plot(result.timestamps, result.equity_curve, label=name, linewidth=2)
        ax1.set_title('资金曲线对比')
        ax1.set_xlabel('时间')
        ax1.set_ylabel('资金')
        ax1.legend()
        ax1.grid(True)
        
        # 回撤曲线
        ax2 = axes[0, 1]
        for name, result in results.items():
            if len(result.equity_curve) > 1:
                peak = np.maximum.accumulate(result.equity_curve)
                drawdown = (np.array(result.equity_curve) - peak) / peak * 100
                ax2.plot(result.timestamps, drawdown, label=name, linewidth=2)
        ax2.set_title('回撤曲线对比')
        ax2.set_xlabel('时间')
        ax2.set_ylabel('回撤 (%)')
        ax2.legend()
        ax2.grid(True)
        
        # 性能指标对比
        ax3 = axes[1, 0]
        metrics = ['annual_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
        strategy_names = list(results.keys())
        x = np.arange(len(metrics))
        width = 0.35
        
        for i, name in enumerate(strategy_names):
            values = [
                results[name].annual_return * 100,
                results[name].sharpe_ratio,
                results[name].max_drawdown * 100,
                results[name].win_rate * 100
            ]
            ax3.bar(x + i * width, values, width, label=name)
        
        ax3.set_title('关键指标对比')
        ax3.set_xlabel('指标')
        ax3.set_ylabel('数值')
        ax3.set_xticks(x + width / 2)
        ax3.set_xticklabels(['年化收益率(%)', '夏普比率', '最大回撤(%)', '胜率(%)'])
        ax3.legend()
        ax3.grid(True)
        
        # 交易分布
        ax4 = axes[1, 1]
        for name, result in results.items():
            returns = [t.return_pct * 100 for t in result.trades if not t.is_open]
            if returns:
                ax4.hist(returns, bins=20, alpha=0.7, label=name)
        ax4.set_title('交易收益率分布')
        ax4.set_xlabel('收益率 (%)')
        ax4.set_ylabel('频次')
        ax4.legend()
        ax4.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_report(self, results: Dict[str, BacktestResult]) -> str:
        """生成回测报告"""
        report = "=" * 60 + "\n"
        report += "策略回测报告\n"
        report += "=" * 60 + "\n\n"
        
        for name, result in results.items():
            report += f"策略: {name}\n"
            report += "-" * 40 + "\n"
            report += f"总收益率: {result.total_return:.2%}\n"
            report += f"年化收益率: {result.annual_return:.2%}\n"
            report += f"夏普比率: {result.sharpe_ratio:.3f}\n"
            report += f"最大回撤: {result.max_drawdown:.2%}\n"
            report += f"胜率: {result.win_rate:.2%}\n"
            report += f"盈亏比: {result.profit_factor:.2f}\n"
            report += f"平均交易收益: {result.avg_trade_return:.2%}\n"
            report += f"最大连续亏损: {result.max_consecutive_losses}\n"
            report += f"Calmar比率: {result.calmar_ratio:.3f}\n"
            report += f"Sortino比率: {result.sortino_ratio:.3f}\n"
            report += f"95% VaR: {result.var_95:.2%}\n"
            report += f"总交易次数: {len([t for t in result.trades if not t.is_open])}\n"
            report += "\n"
        
        return report
